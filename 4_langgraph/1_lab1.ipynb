{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Welcome back to Python Notebooks!\n", "\n", "Did<PERSON> miss me??\n", "\n", "### And welcome to Week 4, Day 2 - introducing <PERSON><PERSON><PERSON><PERSON>!"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from typing import Annotated\n", "from langgraph.graph import StateGraph, START, END\n", "from langgraph.graph.message import add_messages\n", "from dotenv import load_dotenv\n", "from IPython.display import Image, display\n", "import gradio as gr\n", "from langgraph.graph import StateGraph\n", "from langgraph.graph.message import add_messages\n", "from langchain_openai import ChatOpenAI\n", "from pydantic import BaseModel\n", "import random\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Some useful constants\n", "\n", "nouns = [\"Cabbages\", \"Unicorns\", \"Toasters\", \"Penguins\", \"Bananas\", \"Zombies\", \"Rainbows\", \"Eels\", \"Pickles\", \"Muffins\"]\n", "adjectives = [\"outrageous\", \"smelly\", \"pedantic\", \"existential\", \"moody\", \"sparkly\", \"untrustworthy\", \"sarcastic\", \"squishy\", \"haunted\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Our favorite first step! Crew was doing this for us, by the way.\n", "load_dotenv(override=True)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def shout(text: Annotated[str, \"something to be shouted\"]) -> str:\n", "    print(text.upper())\n", "    return text.upper()\n", "\n", "shout(\"hello\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### A word about \"Annotated\"\n", "\n", "You probably know this; type hinting is a feature in Python that lets you specify the type of something:\n", "\n", "`my_favorite_things: List`\n", "\n", "But you may not know this:\n", "\n", "You can also use something called \"Annotated\" to add extra information that somebody else might find useful:\n", "\n", "`my_favorite_things: Annotated[List, \"these are a few of mine\"]`\n", "\n", "LangGraph needs us to use this feature when we define our State object.\n", "\n", "It wants us to tell it what function it should call to update the State with a new value.\n", "\n", "This function is called a **reducer**.\n", "\n", "LangGraph provides a default reducer called `add_messages` which takes care of the most common case.\n", "\n", "And that hopefully explains why the State looks like this.\n", "\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 1: Define the State object\n", "\n", "You can use any python object; but it's most common to use a TypedDict or a Pydantic BaseModel."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["\n", "class State(BaseModel):\n", "        \n", "    messages: Annotated[list, add_messages]\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 2: Start the Graph Builder with this State class"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["graph_builder = StateGraph(State)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 3: Create a Node\n", "\n", "A node can be any python function.\n", "\n", "The reducer that we set before gets automatically called to combine this response with previous responses\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def our_first_node(old_state: State) -> State:\n", "\n", "    reply = f\"{random.choice(nouns)} are {random.choice(adjectives)}\"\n", "    messages = [{\"role\": \"assistant\", \"content\": reply}]\n", "\n", "    new_state = State(messages=messages)\n", "\n", "    return new_state\n", "\n", "graph_builder.add_node(\"first_node\", our_first_node)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 4: C<PERSON> Edges"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["graph_builder.add_edge(START, \"first_node\")\n", "graph_builder.add_edge(\"first_node\", END)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 5: Comp<PERSON> the Graph"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["graph = graph_builder.compile()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### That's it! Showtime!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def chat(user_input: str, history):\n", "    message = {\"role\": \"user\", \"content\": user_input}\n", "    messages = [message]\n", "    state = State(messages=messages)\n", "    result = graph.invoke(state)\n", "    print(result)\n", "    return result[\"messages\"][-1].content\n", "\n", "\n", "gr.ChatInterface(chat, type=\"messages\").launch()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### But why did I show you that?\n", "\n", "To make the point that LangGraph is all about python functions - it doesn't need to involve LLMs!!\n", "\n", "Now we'll do the 5 steps again, but in 1 shot:"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["# Step 1: Define the State object\n", "class State(BaseModel):\n", "    messages: Annotated[list, add_messages]\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# Step 2: Start the Graph Builder with this State class\n", "graph_builder = StateGraph(State)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Step 3: Create a Node\n", "\n", "llm = ChatOpenAI(model=\"gpt-4o-mini\")\n", "\n", "def chatbot_node(old_state: State) -> State:\n", "    response = llm.invoke(old_state.messages)\n", "    new_state = State(messages=[response])\n", "    return new_state\n", "\n", "graph_builder.add_node(\"chatbot\", chatbot_node)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Step 4: Create Edges\n", "graph_builder.add_edge(START, \"chatbot\")\n", "graph_builder.add_edge(\"chatbot\", END)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Step 5: Comp<PERSON> the Graph\n", "graph = graph_builder.compile()\n", "display(Image(graph.get_graph().draw_mermaid_png()))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### That's it! And, let's do this:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def chat(user_input: str, history):\n", "    initial_state = State(messages=[{\"role\": \"user\", \"content\": user_input}])\n", "    result = graph.invoke(initial_state)\n", "    print(result)\n", "    return result['messages'][-1].content\n", "\n", "\n", "gr.ChatInterface(chat, type=\"messages\").launch()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}