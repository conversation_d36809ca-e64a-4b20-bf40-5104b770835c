{"cells": [{"cell_type": "markdown", "id": "b1f7f4f9-c76a-4317-b094-8347f57e9f8f", "metadata": {}, "source": ["### Enable \"App Passwords\" in Gmail (Recommended for Security)\n", "- Go to your Google Account -→ Security.\n", "- Enable 2-Step Verification.\n", "- After enabling, go to \"App Passwords\" and generate a password.\n", "  (If \"App Passwords\" is not found on \"Security\" page, then use \"Google Account Search\")\n", "- Use this app password instead of your regular Gmail password in your Python script."]}, {"cell_type": "markdown", "id": "a65f03fe-51f0-44d1-8075-b12493f10ea1", "metadata": {}, "source": ["### Notes\n", "- Never use your main Gmail password in scripts. Always use an App Password.\n", "- If you get authentication errors, make sure 2-Step Verification and App Passwords are enabled.\n", "- If you want to send HTML emails, use MIMEText(html_body, 'html')."]}, {"cell_type": "code", "execution_count": null, "id": "399daac1-5812-4579-916f-7b7e57a5b131", "metadata": {}, "outputs": [], "source": ["import smtplib\n", "from email.mime.text import MIMEText\n", "\n", "# Your Gmail credentials\n", "gmail_user = '<EMAIL>'\n", "gmail_app_password = 'your_app_password'  # Use the app password generated above\n", "\n", "# Email content\n", "to = '<EMAIL>'\n", "subject = 'Test Email from Python'\n", "body = 'Hello, this is a test email sent from Python!'\n", "\n", "# Create the email\n", "msg = MIMEText(body)\n", "msg['Subject'] = subject\n", "msg['From'] = gmail_user\n", "msg['To'] = to\n", "\n", "# Send the email\n", "try:\n", "    with smtplib.SMTP('smtp.gmail.com', 587) as server:\n", "        server.starttls()  # Secure the connection\n", "        server.login(gmail_user, gmail_app_password)\n", "        server.send_message(msg)\n", "    print('<PERSON><PERSON> sent successfully!')\n", "except Exception as e:\n", "    print(f'Failed to send email: {e}')"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 5}