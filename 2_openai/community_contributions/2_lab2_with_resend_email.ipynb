{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Week 2 Day 2\n", "\n", "Our first Agentic Framework project!!\n", "\n", "Prepare yourself for something ridiculously easy.\n", "\n", "We're going to build a simple Agent system for generating cold sales outreach emails:\n", "1. Agent workflow\n", "2. Use of tools to call functions\n", "3. Agent collaboration via Tools and Handoffs"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Before we start - some setup:\n", "\n", "\n", "Please visit Resend:  https://resend.com\n", "\n", "   Please set up an account - it's free! (at least, for me, right now).\n", "\n", "   Create and copy the API key to .env file as \"RESEND_API_KEY=xxxx\"\n", "\n", "   Verify your domain.   Resend will provide you the MX, TXTs DNS records information.   Then go to your DNS provider and add the Resend records. I use AWS Route53 for domain management, for you might be different.\n", "\n", "(Resend is) for sending emails.)\n", "\n", "Once you've created the account, verified email domain, and update .env file, make sure you replace the respective {email to/from fields} with your email addresses\n", "\n", "That's all.   You are all set to send emails!\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from dotenv import load_dotenv\n", "from agents import Agent, Runner, trace, function_tool\n", "from openai.types.responses import ResponseTextDeltaEvent\n", "from typing import Dict\n", "import requests\n", "import os\n", "import asyncio\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["load_dotenv(override=True)\n", "RESEND_API_KEY = os.getenv(\"RESEND_API_KEY\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Agent workflow"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["instructions1 = \"You are a sales agent working for ComplAI, \\\n", "a company that provides a SaaS tool for ensuring SOC2 compliance and preparing for audits, powered by AI. \\\n", "You write professional, serious cold emails.\"\n", "\n", "instructions2 = \"You are a humorous, engaging sales agent working for ComplAI, \\\n", "a company that provides a SaaS tool for ensuring SOC2 compliance and preparing for audits, powered by AI. \\\n", "You write witty, engaging cold emails that are likely to get a response.\"\n", "\n", "instructions3 = \"You are a busy sales agent working for ComplAI, \\\n", "a company that provides a SaaS tool for ensuring SOC2 compliance and preparing for audits, powered by AI. \\\n", "You write concise, to the point cold emails.\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_agent1 = Agent(\n", "        name=\"Professional Sales Agent\",\n", "        instructions=instructions1,\n", "        model=\"gpt-4o-mini\"\n", ")\n", "\n", "sales_agent2 = Agent(\n", "        name=\"Engaging Sales Agent\",\n", "        instructions=instructions2,\n", "        model=\"gpt-4o-mini\"\n", ")\n", "\n", "sales_agent3 = Agent(\n", "        name=\"Busy Sales Agent\",\n", "        instructions=instructions3,\n", "        model=\"gpt-4o-mini\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "result = Runner.run_streamed(sales_agent1, input=\"Write a cold sales email\")\n", "async for event in result.stream_events():\n", "    if event.type == \"raw_response_event\" and isinstance(event.data, ResponseTextDeltaEvent):\n", "        print(event.data.delta, end=\"\", flush=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["message = \"Write a cold sales email\"\n", "\n", "with trace(\"Parallel cold emails\"):\n", "    results = await asyncio.gather(\n", "        Runner.run(sales_agent1, message),\n", "        Runner.run(sales_agent2, message),\n", "        Runner.run(sales_agent3, message),\n", "    )\n", "\n", "outputs = [result.final_output for result in results]\n", "\n", "for output in outputs:\n", "    print(output + \"\\n\\n\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_picker = Agent(\n", "    name=\"sales_picker\",\n", "    instructions=\"You pick the best cold sales email from the given options. \\\n", "Imagine you are a customer and pick the one you are most likely to respond to. \\\n", "Do not give an explanation; reply with the selected email only.\",\n", "    model=\"gpt-4o-mini\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["message = \"Write a cold sales email\"\n", "\n", "with trace(\"Selection from sales people\"):\n", "    results = await asyncio.gather(\n", "        Runner.run(sales_agent1, message),\n", "        Runner.run(sales_agent2, message),\n", "        Runner.run(sales_agent3, message),\n", "    )\n", "    outputs = [result.final_output for result in results]\n", "\n", "    emails = \"Cold sales emails:\\n\\n\".join(outputs)\n", "\n", "    best = await Runner.run(sales_picker, emails)\n", "\n", "    print(f\"Best sales email:\\n{best.final_output}\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now go and check out the trace:\n", "\n", "https://platform.openai.com/traces"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Part 2: use of tools\n", "\n", "Now we will add a tool to the mix.\n", "\n", "Remember all that json boilerplate and the `handle_tool_calls()` function with the if logic.."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_agent1 = Agent(\n", "        name=\"Professional Sales Agent\",\n", "        instructions=instructions1,\n", "        model=\"gpt-4o-mini\",\n", ")\n", "\n", "sales_agent2 = Agent(\n", "        name=\"Engaging Sales Agent\",\n", "        instructions=instructions2,\n", "        model=\"gpt-4o-mini\",\n", ")\n", "\n", "sales_agent3 = Agent(\n", "        name=\"Busy Sales Agent\",\n", "        instructions=instructions3,\n", "        model=\"gpt-4o-mini\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_agent1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Steps 2 and 3: Too<PERSON> and Agent interactions\n", "\n", "Remember all that boilerplate json?\n", "\n", "Simply wrap your function with the decorator `@function_tool`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["@function_tool\n", "def send_email(body: str):\n", "    \"\"\" Send out an email with the given body to all sales prospects via Resend \"\"\"\n", "    \n", "    # Set up email sender, recipient, and content\n", "    from_email = \"<EMAIL>\"  # Replace with your verified sender\n", "    to_email = \"<EMAIL>\"  # Replace with recipient's email\n", "    \n", "    # Resend API headers and payload\n", "    headers = {\n", "        \"Authorization\": f\"Bearer {RESEND_API_KEY}\",\n", "        \"Content-Type\": \"application/json\"\n", "    }\n", "    \n", "    payload = {\n", "        \"from\": f\"<PERSON> <{from_email}>\",\n", "        \"to\": [to_email],\n", "        \"subject\": \"Sales email\",\n", "        \"html\": f\"<p>{body}</p>\"  # Body wrapped in <p> tags for HTML format\n", "    }\n", "    \n", "    # Send email using Resend API\n", "    response = requests.post(\"https://api.resend.com/emails\", json=payload, headers=headers)\n", "    \n", "    # Check if the request was successful\n", "    if response.status_code == 202:\n", "        return {\"status\": \"success\"}\n", "    else:\n", "        return {\"status\": \"failure\", \"message\": response.text}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### This has automatically been converted into a tool, with the boilerplate json created"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Let's look at it\n", "send_email"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### And you can also convert an Agent into a tool"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tool1 = sales_agent1.as_tool(tool_name=\"sales_agent1\", tool_description=\"Write a cold sales email\")\n", "tool1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### So now we can gather all the tools together:\n", "\n", "A tool for each of our 3 email-writing agents\n", "\n", "And a tool for our function to send emails"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["description = \"Write a cold sales email\"\n", "\n", "tool1 = sales_agent1.as_tool(tool_name=\"sales_agent1\", tool_description=description)\n", "tool2 = sales_agent2.as_tool(tool_name=\"sales_agent2\", tool_description=description)\n", "tool3 = sales_agent3.as_tool(tool_name=\"sales_agent3\", tool_description=description)\n", "\n", "tools = [tool1, tool2, tool3, send_email]\n", "\n", "tools"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## And now it's time for our Sales Manager - our planning agent"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["instructions =\"You are a sales manager working for ComplAI. You use the tools given to you to generate cold sales emails. \\\n", "You never generate sales emails yourself; you always use the tools. \\\n", "You try all 3 sales_agent tools once before choosing the best one. \\\n", "You pick the single best email and use the send_email tool to send the best email (and only the best email) to the user.\"\n", "\n", "\n", "sales_manager = Agent(name=\"Sales Manager\", instructions=instructions, tools=tools, model=\"gpt-4o-mini\")\n", "\n", "message = \"Send a cold sales email addressed to 'Dear CEO'\"\n", "\n", "with trace(\"Sales manager\"):\n", "    result = await Runner.run(sales_manager, message)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../assets/stop.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#ff7800;\">Wait - you didn't get an email??</h2>\n", "            <span style=\"color:#ff7800;\">With much thanks to student <PERSON> for describing his issue and fixes. \n", "            If you don't receive an email after running the prior cell, here are some things to check: <br/>\n", "            First, check your Spam folder! Several students have missed that the emails arrived in Spam!<br/>Second, print(result) and see if you are receiving errors about SSL. \n", "            If you're receiving SSL errors, then please check out theses <a href=\"https://chatgpt.com/share/680620ec-3b30-8012-8c26-ca86693d0e3d\">networking tips</a> particularly the uv command in Part 7. Also look at the trace in OpenAI, and investigate on the SendGrid website, to hunt for clues. Let me know if I can help!\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Remember to check the trace\n", "\n", "https://platform.openai.com/traces\n", "\n", "And then check your email!!\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Handoffs represent a way an agent can delegate to an agent, passing control to it\n", "\n", "Handoffs and Agents-as-tools are similar:\n", "\n", "In both cases, an Agent can collaborate with another Agent\n", "\n", "With tools, control passes back\n", "\n", "With handoffs, control passes across\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "subject_instructions = \"You can write a subject for a cold sales email. \\\n", "You are given a message and you need to write a subject for an email that is likely to get a response.\"\n", "\n", "html_instructions = \"You can convert a text email body to an HTML email body. \\\n", "You are given a text email body which might have some markdown \\\n", "and you need to convert it to an HTML email body with simple, clear, compelling layout and design.\"\n", "\n", "subject_writer = Agent(name=\"Email subject writer\", instructions=subject_instructions, model=\"gpt-4o-mini\")\n", "subject_tool = subject_writer.as_tool(tool_name=\"subject_writer\", tool_description=\"Write a subject for a cold sales email\")\n", "\n", "html_converter = Agent(name=\"HTML email body converter\", instructions=html_instructions, model=\"gpt-4o-mini\")\n", "html_tool = html_converter.as_tool(tool_name=\"html_converter\",tool_description=\"Convert a text email body to an HTML email body\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["@function_tool\n", "def send_html_email(subject: str, html_body: str) -> Dict[str, str]:\n", "    \"\"\"Send out an email with the given subject and HTML body to all sales prospects using Resend\"\"\"\n", "    \n", "    # Replace with your actual verified sender and target recipient\n", "    from_email = \"<EMAIL>\"\n", "    to_email = \"<EMAIL>\"\n", "    \n", "    # Get the Resend API key from environment variable\n", "    RESEND_API_KEY = os.environ.get(\"RESEND_API_KEY\")\n", "    \n", "    headers = {\n", "        \"Authorization\": f\"Bearer {RESEND_API_KEY}\",\n", "        \"Content-Type\": \"application/json\"\n", "    }\n", "    \n", "    payload = {\n", "        \"from\": f\"<PERSON> <{from_email}>\",\n", "        \"to\": [to_email],\n", "        \"subject\": subject,\n", "        \"html\": html_body\n", "    }\n", "    \n", "    response = requests.post(\"https://api.resend.com/emails\", json=payload, headers=headers)\n", "    \n", "    if response.status_code == 202:\n", "        return {\"status\": \"success\"}\n", "    else:\n", "        return {\"status\": \"failure\", \"message\": response.text}\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tools = [subject_tool, html_tool, send_html_email]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tools"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["instructions =\"You are an email formatter and sender. You receive the body of an email to be sent. \\\n", "You first use the subject_writer tool to write a subject for the email, then use the html_converter tool to convert the body to HTML. \\\n", "Finally, you use the send_html_email tool to send the email with the subject and HTML body.\"\n", "\n", "\n", "emailer_agent = Agent(\n", "    name=\"Email Manager\",\n", "    instructions=instructions,\n", "    tools=tools,\n", "    model=\"gpt-4o-mini\",\n", "    handoff_description=\"Convert an email to HTML and send it\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Now we have 3 tools and 1 handoff"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tools = [tool1, tool2, tool3]\n", "handoffs = [emailer_agent]\n", "print(tools)\n", "print(handoffs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sales_manager_instructions = \"You are a sales manager working for ComplAI. You use the tools given to you to generate cold sales emails. \\\n", "You never generate sales emails yourself; you always use the tools. \\\n", "You try all 3 sales agent tools at least once before choosing the best one. \\\n", "You can use the tools multiple times if you're not satisfied with the results from the first try. \\\n", "You select the single best email using your own judgement of which email will be most effective. \\\n", "After picking the email, you handoff to the Email Manager agent to format and send the email.\"\n", "\n", "\n", "sales_manager = Agent(\n", "    name=\"Sales Manager\",\n", "    instructions=sales_manager_instructions,\n", "    tools=tools,\n", "    handoffs=handoffs,\n", "    model=\"gpt-4o-mini\")\n", "\n", "message = \"Send out a cold sales email addressed to Dear CEO from <PERSON>\"\n", "\n", "with trace(\"Automated SDR\"):\n", "    result = await Runner.run(sales_manager, message)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Remember to check the trace\n", "\n", "https://platform.openai.com/traces\n", "\n", "And then check your email!!"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../assets/exercise.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#ff7800;\">Exercise</h2>\n", "            <span style=\"color:#ff7800;\">Can you identify the Agentic design patterns that were used here?<br/>\n", "            What is the 1 line that changed this from being an Agentic \"workflow\" to \"agent\" under Anthropic's definition?<br/>\n", "            Try adding in more tools and Agents! You could have tools that handle the mail merge to send to a list.<br/><br/>\n", "            HARD CHALLENGE: research how you can have SendGrid call a Callback webhook when a user replies to an email,\n", "            Then have the SDR respond to keep the conversation going! This may require some \"vibe coding\" 😂\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../assets/business.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#00bfff;\">Commercial implications</h2>\n", "            <span style=\"color:#00bfff;\">This is immediately applicable to Sales Automation; but more generally this could be applied to  end-to-end automation of any business process through conversations and tools. Think of ways you could apply an Agent solution\n", "            like this in your day job.\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Extra note:\n", "\n", "Google has announced their Agent Development Kit (ADK) which is in early preview. It's still under development, so it's too early for us to cover it here. But it's interesting to note that it looks quite similar to OpenAI Agents SDK. To give you a preview, here's a peak at sample code from ADK:\n", "\n", "```\n", "root_agent = Agent(\n", "    name=\"weather_time_agent\",\n", "    model=\"gemini-2.0-flash\",\n", "    description=\"Agent to answer questions about the time and weather in a city.\",\n", "    instruction=\"You are a helpful agent who can answer user questions about the time and weather in a city.\",\n", "    tools=[get_weather, get_current_time]\n", ")\n", "```\n", "\n", "Well, that looks familiar!"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}