{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## OpenAI Agents SDK with Custom Model - LLama 3.3-70b with Groq Inference"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["# The imports\n", "\n", "from dotenv import load_dotenv\n", "from agents import Agent, Runner, OpenAIChatCompletionsModel\n", "from openai import AsyncOpenAI\n", "import os\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# The usual starting point\n", "load_dotenv(override=True)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# One way to create Custom Model to work with OpenAI Agents SDK\n", "\n", "1. Create a custom OpenAI client - `AsyncOpenAI` in this case\n", "2. Create a model that uses the custom client - `OpenAIChatCompletionsModel` in this example\n", "3. Set the custom model on the OpenAI Agent"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["BASE_URL = \"https://api.groq.com/openai/v1\"\n", "MODEL_NAME='llama-3.3-70b-versatile'\n", "\n", "groq_client = AsyncOpenAI(base_url=BASE_URL, api_key=os.getenv(\"GROQ_API_KEY\"))\n", "\n", "custom_model = OpenAIChatCompletionsModel(model=MODEL_NAME, openai_client=groq_client)"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["\n", "# Make an agent with name, instructions, model\n", "\n", "agent = Agent(name=\"<PERSON><PERSON><PERSON>\", instructions=\"You are a joke teller\", model=custom_model)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run the joke with <PERSON>.run(agent, prompt) then print final_output\n", "result = await <PERSON>.run(agent, \"Tell a joke about Autonomous AI Agents\")\n", "print(result.final_output)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 2}