{"cells": [{"cell_type": "code", "execution_count": null, "id": "f26664cd-df92-4e2e-9701-93e4c72b0780", "metadata": {}, "outputs": [], "source": ["#!uv add loguru pydantic rich tenacity"]}, {"cell_type": "markdown", "id": "c2db3227-611a-4f41-9678-46a86568a58a", "metadata": {}, "source": ["<img src=\"./docs/routing1.jpg\" alt=\"Routing 1\" width=\"1200\"/>\n", "<img src=\"./docs/routing2.jpg\" alt=\"Routing 2\" width=\"1200\"/>"]}, {"cell_type": "code", "execution_count": null, "id": "********", "metadata": {}, "outputs": [], "source": ["from routing import chatInterface"]}, {"cell_type": "code", "execution_count": null, "id": "bc6d2649-9206-4a4f-9d5e-ef241f8750bd", "metadata": {}, "outputs": [], "source": ["chatInterface.launch()"]}], "metadata": {"kernelspec": {"display_name": "agents", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 5}