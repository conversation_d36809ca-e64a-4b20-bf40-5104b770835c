{"cells": [{"cell_type": "code", "execution_count": null, "id": "f26664cd-df92-4e2e-9701-93e4c72b0780", "metadata": {}, "outputs": [], "source": ["#!uv add loguru pydantic rich tenacity"]}, {"cell_type": "markdown", "id": "c2db3227-611a-4f41-9678-46a86568a58a", "metadata": {}, "source": ["<img src=\"./docs/parallelization1.jpg\" alt=\"Parllelizaation 1\" width=\"1200\"/>\n", "<img src=\"./docs/parallelization2.jpg\" alt=\"Parllelizaation 2\" width=\"1200\"/>"]}, {"cell_type": "code", "execution_count": null, "id": "549eec82-0f5b-400a-a1f5-992a42c970b2", "metadata": {}, "outputs": [], "source": ["import asyncio\n", "import os\n", "\n", "from agents import ItemHelpers, Runner, RunResult, trace\n", "from build_agents import ag_coding_explainer, ag_explanation_picker\n", "from rich.console import Console\n", "from rich.markdown import Markdown\n", "\n", "# from IPython.display import Markdown, display\n", "\n", "console = Console()\n", "\n", "# print(f\"INFO | Current working directory: {os.getcwd()}\")\n", "# print(f\"INFO | Available LLMs: {llm_manager._registry.keys()}\")\n", "# print(f\"INFO | Loading .env file success: {load_dotenv(override=True)}\")\n", "# print(f\"INFO | Input CurriculumCheckOutput: {CurriculumCheckOutput.__dir__(BaseModel)}\")\n", "\n", "code_snippet = \"Write a Python function called group_anagrams that takes a list of strings and groups the anagrams together. \"\n", "\n", "# Run three parallel explanation attempts\n", "with trace(workflow_name=\"Parallelization\"):\n", "    res_1, res_2, res_3 = await asyncio.gather(\n", "        Runner.run(starting_agent=ag_coding_explainer, input=code_snippet),\n", "        Runner.run(starting_agent=ag_coding_explainer, input=code_snippet),\n", "        Runner.run(starting_agent=ag_coding_explainer, input=code_snippet),\n", "    )\n", "\n", "    explanations: list[str] = [\n", "        ItemHelpers.text_message_outputs(items=res_1.new_items),\n", "        ItemHelpers.text_message_outputs(items=res_2.new_items),\n", "        ItemHelpers.text_message_outputs(items=res_3.new_items),\n", "    ]\n", "\n", "    all_explanations: str = \"\\n\\n\".join(explanations)\n", "    console.print(\n", "        Markdown(markup=f\"\\n\\nCandidate Explanations:\\n\\n{all_explanations}\")\n", "    )\n", "\n", "    # Select the best explanation\n", "    best_explanation: RunResult = await Runner.run(\n", "        starting_agent=ag_explanation_picker,\n", "        input=f\"Code:\\n{code_snippet}\\n\\nExplanations:\\n{all_explanations}\",\n", "    )\n", "print(\"\\n\\n-----\")\n", "console.print(\n", "    Markdown(markup=f\"Best explanation:\\n{best_explanation.final_output}\")\n", ")\n"]}], "metadata": {"kernelspec": {"display_name": "agents", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 5}