{"cells": [{"cell_type": "code", "execution_count": null, "id": "f26664cd-df92-4e2e-9701-93e4c72b0780", "metadata": {}, "outputs": [], "source": ["# !uv add loguru pydantic rich tenacity"]}, {"cell_type": "markdown", "id": "c2db3227-611a-4f41-9678-46a86568a58a", "metadata": {}, "source": ["<img src=\"./docs/prompt_chaining1.jpg\" alt=\"Prompt Chaining\" width=\"1200\"/>\n", "<img src=\"./docs/prompt_chaining2.jpg\" alt=\"Prompt Chaining\" width=\"1200\"/>"]}, {"cell_type": "code", "execution_count": null, "id": "549eec82-0f5b-400a-a1f5-992a42c970b2", "metadata": {}, "outputs": [], "source": ["import asyncio\n", "import os\n", "\n", "from agents import Runner, RunResult, trace\n", "from build_agents import curriculum_agent, curriculum_checker_agent, lesson_writer_agent\n", "from configs import CurriculumCheckOutput\n", "from rich.console import Console\n", "from rich.markdown import Markdown\n", "\n", "# from IPython.display import Markdown, display\n", "\n", "console = Console()\n", "\n", "# learning_goal: str = input(input_prompt)\n", "learning_goal = \"I want to learn Python \"\n", "\n", "with trace(workflow_name=\"Deterministic tutor flow\"):\n", "    # 1. Generate curriculum\n", "    print(f\"INFO | Generating curriculum for goal: {learning_goal}\")\n", "    curriculum_result: RunResult = await Runner.run(\n", "        starting_agent=curriculum_agent,\n", "        input=learning_goal,\n", "    )\n", "    console.print(\n", "        Markdown(f\"Curriculum generated.\\n{curriculum_result.final_output}\\n\")\n", "    )\n", "    \n", "    # 2. Check the curriculum\n", "    print(\"INFO | Checking the curriculum quality and goal match...\")\n", "    check_result: RunResult = await Runner.run(\n", "        starting_agent=curriculum_checker_agent,\n", "        input=curriculum_result.final_output,\n", "    )\n", "    \n", "    assert isinstance(check_result.final_output, CurriculumCheckOutput)\n", "    if not check_result.final_output.good_quality:\n", "        print(\"\\n\")\n", "        print(\"INFO | Curriculum is low quality. Stopping.\")\n", "        exit(0)\n", "    \n", "    if not check_result.final_output.matches_goal:\n", "        print(\"\\n\")\n", "        print(\"INFO | Curriculum doesn't match the learning goal. Stopping.\")\n", "        exit(0)\n", "    \n", "    print(\"\\nINFO | Curriculum looks good. Proceeding to generate lessons...\")\n", "    \n", "    # 3. Generate lessons\n", "    lessons_result: RunResult = await Runner.run(\n", "        starting_agent=lesson_writer_agent,\n", "        input=curriculum_result.final_output,\n", "    )\n", "    console.print(Markdown(f\"\\nGenerated Lessons:\\n{lessons_result.final_output}\"))"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 5}