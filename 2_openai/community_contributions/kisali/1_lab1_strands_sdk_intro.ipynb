{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Strands Agents with DeepSeek and Amazon Bedrock\n", "\n", "I'll be using AWS Strands Agents SDK instead of Open AI Agents SDK, in addition to Deepseek API directly. This will enable me use different models available on Amazon Bedrock."]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../../../assets/tools.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#00bfff;\">The Strands Agents SDK Docs</h2>\n", "            <span style=\"color:#00bfff;\">The documentation on Amazon Strands Agents SDK is really clear and simple: <a href=\"https://strandsagents.com/latest/documentation/docs/\">https://strandsagents.com/latest/documentation/docs/</a> and it's well worth a look.\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Strands Agents with DeepSeek and Amazon Bedrock\n", "This notebook demonstrates how to create basic Strands agents using models from DeepSeek API and Amazon Bedrock.\n", "\n", " **Prerequisites:**\n", " - Python virtual environment set up.\n", " - `strands-agents`, `strands-agents-tools`, `litellm`, `boto3`, `python-dotenv` installed.\n", " - DeepSeek API Key configured as `DEEPSEEK_API_KEY` environment variable (e.g., in a `.env` file).\n", " - Bedrock API Key configured as `AMAZON_BEDROCK_API_KEY` environment variable (e.g., in a `.env` file).\n", " - AWS CLI configured with credentials that have access to Amazon Bedrock and desired models.\n", " - Desired models enabled in Amazon Bedrock console."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Importing necessary components\n", "from strands.models import BedrockModel\n", "from strands_tools import calculator\n", "from dotenv import load_dotenv\n", "from strands import Agent\n", "import os \n", "#import boto3"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Loading environment variables from .env file\n", "load_dotenv(override=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1. Strands Agent with Amazon Bedrock\n", "\n", " Strands SDK has native integration with Amazon Bedrock using the `BedrockModel` class.\n", "\n", " Initialize the Bedrock model\n", " - Ensure your AWS credentials are configured (e.g., via `aws configure`) and you have access to the specified model in the correct region.\n", " - Example model_id for Claude 3.5 Sonnet in us-east-1: \"anthropic.claude-3-sonnet-20240229-v1:0\"\n", " - Example model_id for Amazon's own model: \"amazon.titan-text-express-v1\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bedrock_model = BedrockModel(\n", "    model_id=\"anthropic.claude-3-5-sonnet-20240620-v1:0\",\n", "    region_name=\"us-east-1\", \n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- Create a Strands Agent for Bedrock"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create an agent with the configured model\n", "joke_agent = Agent(name=\"<PERSON><PERSON><PERSON>\", \n", "                    system_prompt=\"You are a joke teller\", \n", "                    model=bedrock_model)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- Running Bedrock agent with a query"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Use the agent\n", "response = joke_agent(\"Tell me a joke about Agentic AI.\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["bedrock_model = BedrockModel(\n", "    model_id=\"amazon.nova-pro-v1:0\",\n", "    region_name=\"us-east-1\", \n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- Creating a strands agent using amazon nova pro using calculator tool available as one of strands tools"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["math_agent = Agent(name=\"Math Agent\", \n", "                    system_prompt=\"You are a helpful math assistant\", \n", "                    model=bedrock_model, \n", "                    tools=[calculator])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response = math_agent(\"What is the area of a triangle whose height is 16 ad base is 20?\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1. Strands Agent with DeepSeek API (via LiteLLM)\n", "\n", "- DeepSeek's API is OpenAI-compatible, so we can leverage LiteLLM to connect to it seamlessly.\n", "\n", "- Initialize the LiteLLM model for DeepSeek\n", "- We specify the model name which LiteLLM maps to DeepSeek's service.\n", "- You can choose 'deepseek/deepseek-chat' (V3) or 'deepseek/deepseek-reasoner' (R1)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["deepseek_api_key = os.getenv(\"DEEPSEEK_API_KEY\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from strands.models.litellm import LiteLLMModel"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = LiteLLMModel(\n", "    client_args={\n", "        \"api_key\": deepseek_api_key,\n", "    },\n", "    model_id=\"deepseek/deepseek-chat\",\n", "    params={\n", "        \"max_tokens\": 1000,\n", "        \"temperature\": 0.7,\n", "    }\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- Create a Strands Agent for DeepSeek"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["agent = Agent(model=model)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response_1 = agent(\"What is an ai agent\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}}, "nbformat": 4, "nbformat_minor": 2}