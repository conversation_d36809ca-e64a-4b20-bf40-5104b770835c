{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Week 2 Day 1\n", "\n", "And now! Our first look at OpenAI Agents SDK\n", "\n", "You won't believe how lightweight this is.."]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../assets/tools.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#00bfff;\">The OpenAI Agents SDK Docs</h2>\n", "            <span style=\"color:#00bfff;\">The documentation on OpenAI Agents SDK is really clear and simple: <a href=\"https://openai.github.io/openai-agents-python/\">https://openai.github.io/openai-agents-python/</a> and it's well worth a look.\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# The imports\n", "\n", "from dotenv import load_dotenv\n", "from agents import Agent, Runner, trace\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# The usual starting point\n", "\n", "load_dotenv(override=True)\n"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["\n", "# Make an agent with name, instructions, model\n", "\n", "agent = Agent(name=\"<PERSON><PERSON><PERSON>\", instructions=\"You are a joke teller\", model=\"gpt-4o-mini\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Run the joke with <PERSON>.run(agent, prompt) then print final_output\n", "\n", "with trace(\"Telling a joke\"):\n", "    result = await <PERSON>.run(agent, \"Tell a joke about Autonomous AI Agents\")\n", "    print(result.final_output)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Now go and look at the trace\n", "\n", "https://platform.openai.com/traces"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}