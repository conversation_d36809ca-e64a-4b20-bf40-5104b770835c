{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Welcome to the Second Lab - Week 1, Day 3\n", "\n", "Today we will work with lots of models! This is a way to get comfortable with APIs."]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../assets/stop.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#ff7800;\">Important point - please read</h2>\n", "            <span style=\"color:#ff7800;\">The way I collaborate with you may be different to other courses you've taken. I prefer not to type code while you watch. Rather, I execute Jupyter Labs, like this, and give you an intuition for what's going on. My suggestion is that you carefully execute this yourself, <b>after</b> watching the lecture. Add print statements to understand what's going on, and then come up with your own variations.<br/><br/>If you have time, I'd love it if you submit a PR for changes in the community_contributions folder - instructions in the resources. Also, if you have a Github account, use this to showcase your variations. Not only is this essential practice, but it demonstrates your skills to others, including perhaps future clients or employers...\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Start with imports - ask ChatGPT to explain any package that you don't know\n", "# Course_AIAgentic\n", "import os\n", "import json\n", "from collections import defaultdict\n", "from dotenv import load_dotenv\n", "from openai import OpenAI\n", "from anthropic import Anthropic\n", "from IPython.display import Markdown, display"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Always remember to do this!\n", "load_dotenv(override=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Print the key prefixes to help with any debugging\n", "\n", "openai_api_key = os.getenv('OPENAI_API_KEY')\n", "anthropic_api_key = os.getenv('ANTHROPIC_API_KEY')\n", "google_api_key = os.getenv('GOOGLE_API_KEY')\n", "deepseek_api_key = os.getenv('DEEPSEEK_API_KEY')\n", "groq_api_key = os.getenv('GROQ_API_KEY')\n", "\n", "if openai_api_key:\n", "    print(f\"OpenAI API Key exists and begins {openai_api_key[:8]}\")\n", "else:\n", "    print(\"OpenAI API Key not set\")\n", "    \n", "if anthropic_api_key:\n", "    print(f\"Anthropic API Key exists and begins {anthropic_api_key[:7]}\")\n", "else:\n", "    print(\"Anthropic API Key not set (and this is optional)\")\n", "\n", "if google_api_key:\n", "    print(f\"Google API Key exists and begins {google_api_key[:2]}\")\n", "else:\n", "    print(\"Google API Key not set (and this is optional)\")\n", "\n", "if deepseek_api_key:\n", "    print(f\"DeepSeek API Key exists and begins {deepseek_api_key[:3]}\")\n", "else:\n", "    print(\"DeepSeek API Key not set (and this is optional)\")\n", "\n", "if groq_api_key:\n", "    print(f\"Groq API Key exists and begins {groq_api_key[:4]}\")\n", "else:\n", "    print(\"Groq API Key not set (and this is optional)\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["request = \"Please come up with a challenging, nuanced question that I can ask a number of LLMs to evaluate their intelligence. \"\n", "request += \"Answer only with the question, no explanation.\"\n", "messages = [{\"role\": \"user\", \"content\": request}]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["messages"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["openai = OpenAI()\n", "response = openai.chat.completions.create(\n", "    model=\"gpt-4o-mini\",\n", "    messages=messages,\n", ")\n", "question = response.choices[0].message.content\n", "print(question)\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["competitors = []\n", "answers = []\n", "messages = [{\"role\": \"user\", \"content\": question}]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# The API we know well\n", "\n", "model_name = \"gpt-4o-mini\"\n", "\n", "response = openai.chat.completions.create(model=model_name, messages=messages)\n", "answer = response.choices[0].message.content\n", "\n", "display(<PERSON><PERSON>(answer))\n", "competitors.append(model_name)\n", "answers.append(answer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Anthropic has a slightly different API, and <PERSON> Tokens is required\n", "\n", "model_name = \"claude-3-7-sonnet-latest\"\n", "\n", "claude = Anthropic()\n", "response = claude.messages.create(model=model_name, messages=messages, max_tokens=1000)\n", "answer = response.content[0].text\n", "\n", "display(<PERSON><PERSON>(answer))\n", "competitors.append(model_name)\n", "answers.append(answer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gemini = OpenAI(api_key=google_api_key, base_url=\"https://generativelanguage.googleapis.com/v1beta/openai/\")\n", "model_name = \"gemini-2.0-flash\"\n", "\n", "response = gemini.chat.completions.create(model=model_name, messages=messages)\n", "answer = response.choices[0].message.content\n", "\n", "display(<PERSON><PERSON>(answer))\n", "competitors.append(model_name)\n", "answers.append(answer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["deepseek = OpenAI(api_key=deepseek_api_key, base_url=\"https://api.deepseek.com/v1\")\n", "model_name = \"deepseek-chat\"\n", "\n", "response = deepseek.chat.completions.create(model=model_name, messages=messages)\n", "answer = response.choices[0].message.content\n", "\n", "display(<PERSON><PERSON>(answer))\n", "competitors.append(model_name)\n", "answers.append(answer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["groq = OpenAI(api_key=groq_api_key, base_url=\"https://api.groq.com/openai/v1\")\n", "model_name = \"llama-3.3-70b-versatile\"\n", "\n", "response = groq.chat.completions.create(model=model_name, messages=messages)\n", "answer = response.choices[0].message.content\n", "\n", "display(<PERSON><PERSON>(answer))\n", "competitors.append(model_name)\n", "answers.append(answer)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## For the next cell, we will use Ollama\n", "\n", "Ollama runs a local web service that gives an OpenAI compatible endpoint,  \n", "and runs models locally using high performance C++ code.\n", "\n", "If you don't have <PERSON><PERSON><PERSON>, install it here by visiting https://ollama.com then pressing Download and following the instructions.\n", "\n", "After it's installed, you should be able to visit here: http://localhost:11434 and see the message \"<PERSON><PERSON><PERSON> is running\"\n", "\n", "You might need to restart Cursor (and maybe reboot). Then open a Terminal (control+\\`) and run `ollama serve`\n", "\n", "Useful Ollama commands (run these in the terminal, or with an exclamation mark in this notebook):\n", "\n", "`ollama pull <model_name>` downloads a model locally  \n", "`ollama ls` lists all the models you've downloaded  \n", "`ollama rm <model_name>` deletes the specified model from your downloads"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../assets/stop.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#ff7800;\">Super important - ignore me at your peril!</h2>\n", "            <span style=\"color:#ff7800;\">The model called <b>llama3.3</b> is FAR too large for home computers - it's not intended for personal computing and will consume all your resources! Stick with the nicely sized <b>llama3.2</b> or <b>llama3.2:1b</b> and if you want larger, try llama3.1 or smaller variants of <PERSON><PERSON>, <PERSON>, <PERSON> or DeepSeek. See the <A href=\"https://ollama.com/models\">the Ollama models page</a> for a full list of models and sizes.\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!ollama pull llama3.2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ollama = OpenAI(base_url='http://192.168.1.60:11434/v1', api_key='ollama')\n", "model_name = \"llama3.2\"\n", "\n", "response = ollama.chat.completions.create(model=model_name, messages=messages)\n", "answer = response.choices[0].message.content\n", "\n", "display(<PERSON><PERSON>(answer))\n", "competitors.append(model_name)\n", "answers.append(answer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# So where are we?\n", "\n", "print(competitors)\n", "print(answers)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# It's nice to know how to use \"zip\"\n", "for competitor, answer in zip(competitors, answers):\n", "    print(f\"Competitor: {competitor}\\n\\n{answer}\\n\\n\")\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["# Let's bring this together - note the use of \"enumerate\"\n", "\n", "together = \"\"\n", "for index, answer in enumerate(answers):\n", "    together += f\"# Response from competitor {index+1}\\n\\n\"\n", "    together += answer + \"\\n\\n\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(together)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["judge = f\"\"\"You are judging a competition between {len(competitors)} competitors.\n", "Each model has been given this question:\n", "\n", "{question}\n", "\n", "Your job is to evaluate each response for clarity and strength of argument, and rank them in order of best to worst.\n", "Respond with JSON, and only JSON, with the following format:\n", "{{\"results\": [\"best competitor number\", \"second best competitor number\", \"third best competitor number\", ...]}}\n", "\n", "Here are the responses from each competitor:\n", "\n", "{together}\n", "\n", "Now respond with the JSON with the ranked order of the competitors, nothing else. Do not include markdown formatting or code blocks.\"\"\"\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(judge)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["judge_messages = [{\"role\": \"user\", \"content\": judge}]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Judgement time!\n", "\n", "openai = OpenAI()\n", "response = openai.chat.completions.create(\n", "    model=\"o3-mini\",\n", "    messages=judge_messages,\n", ")\n", "results = response.choices[0].message.content\n", "print(results)\n", "\n", "# remove openai variable\n", "del openai"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# OK let's turn this into results!\n", "\n", "results_dict = json.loads(results)\n", "ranks = results_dict[\"results\"]\n", "for index, result in enumerate(ranks):\n", "    competitor = competitors[int(result)-1]\n", "    print(f\"Rank {index+1}: {competitor}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## ranking system for various models to get a true winner\n", "\n", "cross_model_results = []\n", "\n", "for competitor in competitors:\n", "    judge = f\"\"\"You are judging a competition between {len(competitors)} competitors.\n", "                Each model has been given this question:\n", "\n", "                {question}\n", "\n", "                Your job is to evaluate each response for clarity and strength of argument, and rank them in order of best to worst.\n", "                Respond with JSON, and only JSON, with the following format:\n", "                {{\"{competitor}\": [\"best competitor number\", \"second best competitor number\", \"third best competitor number\", ...]}}\n", "\n", "                Here are the responses from each competitor:\n", "\n", "                {together}\n", "\n", "                Now respond with the JSON with the ranked order of the competitors, nothing else. Do not include markdown formatting or code blocks.\"\"\"\n", "    \n", "    judge_messages = [{\"role\": \"user\", \"content\": judge}]\n", "\n", "    if competitor.lower().startswith(\"claude\"):\n", "        claude = Anthropic()\n", "        response = claude.messages.create(model=competitor, messages=judge_messages, max_tokens=1024)\n", "        results = response.content[0].text\n", "        #memory cleanup\n", "        del claude\n", "    else:\n", "        openai = OpenAI()\n", "        response = openai.chat.completions.create(\n", "            model=\"o3-mini\",\n", "            messages=judge_messages,\n", "        )\n", "        results = response.choices[0].message.content\n", "        #memory cleanup\n", "        del openai\n", "\n", "    cross_model_results.append(results)\n", "\n", "print(cross_model_results)\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# Dictionary to store cumulative scores for each model\n", "model_scores = defaultdict(int)\n", "model_names = {}\n", "\n", "# Create mapping from model index to model name\n", "for i, name in enumerate(competitors, 1):\n", "    model_names[str(i)] = name\n", "\n", "# Process each ranking\n", "for result_str in cross_model_results:\n", "    result = json.loads(result_str)\n", "    evaluator_name = list(result.keys())[0]\n", "    rankings = result[evaluator_name]\n", "    \n", "    #print(f\"\\n{evaluator_name} rankings:\")\n", "    # Convert rankings to scores (rank 1 = score 1, rank 2 = score 2, etc.)\n", "    for rank_position, model_id in enumerate(rankings, 1):\n", "        model_name = model_names.get(model_id, f\"Model {model_id}\")\n", "        model_scores[model_id] += rank_position\n", "        #print(f\"  Rank {rank_position}: {model_name} (Model {model_id})\")\n", "\n", "print(\"\\n\" + \"=\"*70)\n", "print(\"AGGREGATED RESULTS (lower score = better performance):\")\n", "print(\"=\"*70)\n", "\n", "# Sort models by total score (ascending - lower is better)\n", "sorted_models = sorted(model_scores.items(), key=lambda x: x[1])\n", "\n", "for rank, (model_id, total_score) in enumerate(sorted_models, 1):\n", "    model_name = model_names.get(model_id, f\"Model {model_id}\")\n", "    avg_score = total_score / len(cross_model_results)\n", "    print(f\"Rank {rank}: {model_name} (Model {model_id}) - Total Score: {total_score}, Average Score: {avg_score:.2f}\")\n", "\n", "winner_id = sorted_models[0][0]\n", "winner_name = model_names.get(winner_id, f\"Model {winner_id}\")\n", "print(f\"\\n🏆 WINNER: {winner_name} (Model {winner_id}) with the lowest total score of {sorted_models[0][1]}\")\n", "\n", "# Show detailed breakdown\n", "print(f\"\\n📊 DETAILED BREAKDOWN:\")\n", "print(\"-\" * 50)\n", "for model_id, total_score in sorted_models:\n", "    model_name = model_names.get(model_id, f\"Model {model_id}\")\n", "    print(f\"{model_name}: {total_score} points across {len(cross_model_results)} evaluations\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../assets/exercise.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#ff7800;\">Exercise</h2>\n", "            <span style=\"color:#ff7800;\">Which pattern(s) did this use? Try updating this to add another Agentic design pattern.\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../assets/business.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#00bfff;\">Commercial implications</h2>\n", "            <span style=\"color:#00bfff;\">These kinds of patterns - to send a task to multiple models, and evaluate results,\n", "            and common where you need to improve the quality of your LLM response. This approach can be universally applied\n", "            to business projects where accuracy is critical.\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 2}