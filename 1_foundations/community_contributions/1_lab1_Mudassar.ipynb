{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# First Agentic AI workflow with OPENAI"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### And please do remember to contact me if I can help\n", "\n", "And I love to connect: https://www.linkedin.com/in/muham<PERSON>-mudassar-a65645192/"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Import Libraries"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [], "source": ["import os\n", "import re\n", "from openai import OpenAI\n", "from dotenv import load_dotenv\n", "from IPython.display import Markdown, display"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["load_dotenv(override=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["openai_api_key=os.getenv(\"OPENAI_API_KEY\")\n", "if openai_api_key:\n", "    print(f\"openai api key exists and begins {openai_api_key[:8]}\")\n", "else:\n", "    print(\"OpenAI API Key not set - please head to the troubleshooting guide in the gui\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Workflow with OPENAI"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["openai=OpenAI()"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["message = [{'role':'user','content':\"what is 2+3?\"}]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response = openai.chat.completions.create(model=\"gpt-4o-mini\",messages=message)\n", "print(response.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["question = \"Please propose a hard, challenging question to assess someone's IQ. Respond only with the question.\"\n", "message=[{'role':'user','content':question}]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response=openai.chat.completions.create(model=\"gpt-4o-mini\",messages=message)\n", "question=response.choices[0].message.content\n", "print(f\"Answer: {question}\")"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["message=[{'role':'user','content':question}]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response=openai.chat.completions.create(model=\"gpt-4o-mini\",messages=message)\n", "answer = response.choices[0].message.content\n", "print(f\"Answer: {answer}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# convert \\[ ... \\] to $$ ... $$, to properly render Latex\n", "converted_answer = re.sub(r'\\\\[\\[\\]]', '$$', answer)\n", "display(Markdown(converted_answer))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Exercise"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../../assets/exercise.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <span style=\"color:#ff7800;\">Now try this commercial application:<br/>\n", "            First ask the LLM to pick a business area that might be worth exploring for an Agentic AI opportunity.<br/>\n", "            Then ask the LLM to present a pain-point in that industry - something challenging that might be ripe for an Agentic solution.<br/>\n", "            Finally have 3 third LLM call propose the Agentic AI solution.\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [], "source": ["message = [{'role':'user','content':\"give me a business area related to ecommerce that might be worth exploring for a agentic opportunity.\"}]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response = openai.chat.completions.create(model=\"gpt-4o-mini\",messages=message)\n", "business_area = response.choices[0].message.content\n", "business_area"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["message = business_area + \"present a pain-point in that industry - something challenging that might be ripe for an agentic solutions.\"\n", "message"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["message = [{'role': 'user', 'content': message}]\n", "response = openai.chat.completions.create(model=\"gpt-4o-mini\",messages=message)\n", "question=response.choices[0].message.content\n", "question"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["message=[{'role':'user','content':question}]\n", "response=openai.chat.completions.create(model=\"gpt-4o-mini\",messages=message)\n", "answer=response.choices[0].message.content\n", "print(answer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["display(<PERSON><PERSON>(answer))"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.5"}}, "nbformat": 4, "nbformat_minor": 2}