{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Welcome to the start of your adventure in Agentic AI"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../assets/stop.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#ff7800;\">Are you ready for action??</h2>\n", "            <span style=\"color:#ff7800;\">Have you completed all the setup steps in the <a href=\"../setup/\">setup</a> folder?<br/>\n", "            Have you checked out the guides in the <a href=\"../guides/01_intro.ipynb\">guides</a> folder?<br/>\n", "            Well in that case, you're ready!!\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../assets/tools.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#00bfff;\">This code is a live resource - keep an eye out for my updates</h2>\n", "            <span style=\"color:#00bfff;\">I push updates regularly. As people ask questions or have problems, I add more examples and improve explanations. As a result, the code below might not be identical to the videos, as I've added more steps and better comments. Consider this like an interactive book that accompanies the lectures.<br/><br/>\n", "            I try to send emails regularly with important updates related to the course. You can find this in the 'Announcements' section of Udemy in the left sidebar. You can also choose to receive my emails via your Notification Settings in Udemy. I'm respectful of your inbox and always try to add value with my emails!\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### And please do remember to contact me if I can help\n", "\n", "And I love to connect: https://www.linkedin.com/in/eddonner/\n", "\n", "\n", "### New to Notebooks like this one? Head over to the guides folder!\n", "\n", "Just to check you've already added the Python and Jupyter extensions to Cursor, if not already installed:\n", "- Open extensions (View >> extensions)\n", "- Search for python, and when the results show, click on the ms-python one, and Install it if not already installed\n", "- Search for jupyter, and when the results show, click on the Microsoft one, and Install it if not already installed  \n", "Then View >> Explorer to bring back the File Explorer.\n", "\n", "And then:\n", "1. Click where it says \"Select Kernel\" near the top right, and select the option called `.venv (Python 3.12.9)` or similar, which should be the first choice or the most prominent choice. You may need to choose \"Python Environments\" first.\n", "2. <PERSON>lick in each \"cell\" below, starting with the cell immediately below this text, and press Shift+Enter to run\n", "3. Enjoy!\n", "\n", "After you click \"Select Kernel\", if there is no option like `.venv (Python 3.12.9)` then please do the following:  \n", "1. On Mac: From the Cursor menu, choose Settings >> VS Code Settings (NOTE: be sure to select `VSCode Settings` not `Cursor Settings`);  \n", "On Windows PC: From the File menu, choose Preferences >> VS Code Settings(NOTE: be sure to select `VSCode Settings` not `Cursor Settings`)  \n", "2. In the Settings search bar, type \"venv\"  \n", "3. In the field \"Path to folder with a list of Virtual Environments\" put the path to the project root, like C:\\Users\\<USER>\\projects\\agents (on a Windows PC) or /Users/<USER>/projects/agents (on Mac or Linux).  \n", "And then try again.\n", "\n", "Having problems with missing Python versions in that list? Have you ever used Anaconda before? It might be interferring. Quit Cursor, bring up a new command line, and make sure that your Anaconda environment is deactivated:    \n", "`conda deactivate`  \n", "And if you still have any problems with conda and python versions, it's possible that you will need to run this too:  \n", "`conda config --set auto_activate_base false`  \n", "and then from within the Agents directory, you should be able to run `uv python list` and see the Python 3.12 version."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["from dotenv import load_dotenv"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["# Next it's time to load the API keys into environment variables\n", "\n", "load_dotenv(override=True)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["OpenAI API Key exists and begins sk-proj-\n"]}], "source": ["# Check the keys\n", "\n", "import os\n", "openai_api_key = os.getenv('OPENAI_API_KEY')\n", "\n", "if openai_api_key:\n", "    print(f\"OpenAI API Key exists and begins {openai_api_key[:8]}\")\n", "else:\n", "    print(\"OpenAI API Key not set - please head to the troubleshooting guide in the setup folder\")\n", "    \n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["# And now - the all important import statement\n", "# If you get an import error - head over to troubleshooting guide\n", "\n", "from openai import OpenAI"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [], "source": ["# And now we'll create an instance of the OpenAI class\n", "# If you're not sure what it means to create an instance of a class - head over to the guides folder!\n", "# If you get a NameError - head over to the guides folder to learn about NameErrors\n", "\n", "openai = OpenAI(base_url=\"http://localhost:11434/v1\", api_key=\"ollama\")"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["# Create a list of messages in the familiar OpenAI format\n", "\n", "messages = [{\"role\": \"user\", \"content\": \"What is 2+2?\"}]"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["What is the sum of the reciprocals of the numbers 1 through 10 solved in two distinct, equally difficult ways?\n"]}], "source": ["# And now call it! Any problems, head to the troubleshooting guide\n", "# This uses GPT 4.1 nano, the incredibly cheap model\n", "\n", "MODEL = \"llama3.2:1b\"\n", "response = openai.chat.completions.create(\n", "    model=MODEL,\n", "    messages=messages\n", ")\n", "\n", "print(response.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["# And now - let's ask for a question:\n", "\n", "question = \"Please propose a hard, challenging question to assess someone's IQ. Respond only with the question.\"\n", "messages = [{\"role\": \"user\", \"content\": question}]\n"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["What is the mathematical proof of the Navier-Stokes Equations under time-reversal symmetry for incompressible fluids?\n"]}], "source": ["# ask it - this uses GPT 4.1 mini, still cheap but more powerful than nano\n", "\n", "response = openai.chat.completions.create(\n", "    model=MODEL,\n", "    messages=messages\n", ")\n", "\n", "question = response.choices[0].message.content\n", "\n", "print(question)\n"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [], "source": ["# form a new messages list\n", "messages = [{\"role\": \"user\", \"content\": question}]\n"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The Navier-Stokes Equations (NSE) are a set of nonlinear partial differential equations that describe the motion of fluids. Under time-reversal symmetry, i.e., if you reverse the direction of time, the solution remains unchanged.\n", "\n", "In general, the NSE can be written as:\n", "\n", "∇ ⋅ v = 0\n", "∂v/∂t + v ∇ v = -1/ρ ∇ p\n", "\n", "where v is the velocity field, ρ is the density, and p is the pressure.\n", "\n", "To prove that these equations hold under time-reversal symmetry, we can follow a step-by-step approach:\n", "\n", "**Step 1: Homogeneity**: Suppose you have an incompressible fluid, i.e., ρv = ρ and v · v = 0. If you reverse time, then the density remains constant (ρ ∝ t^(-2)), so we have ρ(∂t/∂t + ∇ ⋅ v) = ∂ρ/∂t.\n", "\n", "Using the product rule and the vector identity for divergence, we can rewrite this as:\n", "\n", "∂ρ/∂t = ∂p/(∇ ⋅ p).\n", "\n", "Since p is a function of v only (because of homogeneity), we have:\n", "\n", "∂p/∂v = 0, which implies that ∂p/∂t = 0.\n", "\n", "**Step 2: Uniqueness**: Suppose there are two solutions to the NSE, u_1 and u_2. If you reverse time, then:\n", "\n", "u_1' = -u_2'\n", "\n", "where \"'\" denotes the inverse of the negative sign. Using the equation v + ∇v = (-1/ρ)∇p, we can rewrite this as:\n", "\n", "∂u_2'/∂t = 0.\n", "\n", "Integrating both sides with respect to time, we get:\n", "\n", "u_2' = u_2\n", "\n", "So, u_2 and u_1 are equivalent under time reversal.\n", "\n", "**Step 3: Conserved charge**: Let's consider a flow field v(x,t) subject to the boundary conditions (<PERSON><PERSON><PERSON> or <PERSON>) at a fixed point x. These boundary conditions imply that there is no flux through the surface of the fluid, so:\n", "\n", "∫_S v · n dS = 0.\n", "\n", "where n is the outward unit normal vector to the surface S bounding the domain D containing the flow field. Since ρv = ρ and v · v = 0 (from time reversal), we have that the total charge Q within the fluid remains conserved:\n", "\n", "∫_D ρ(du/dt + ∇ ⋅ v) dV = Q.\n", "\n", "Since u = du/dt, we can rewrite this as:\n", "\n", "∃Q'_T such that ∑u_i' = -∮v · n dS.\n", "\n", "Taking the limit as time goes to infinity and summing over all fluid particles on a closed surface S (this is possible because the flow field v(x,t) is assumed to be conservative for long times), we get:\n", "\n", "Q_u = -∆p, where p_0 = ∂p/∂v evaluated on the initial condition.\n", "\n", "**Step 4: Time reversal invariance**: Now that we have shown both time homogeneity and uniqueness under time reversal, let's consider what happens to the NSE:\n", "\n", "∇ ⋅ v = ρvu'\n", "∂v/∂t + ∇(u ∇ v) = -1/ρ ∇ p'\n", "\n", "We can swap the order of differentiation with respect to t and evaluate each term separately:\n", "\n", "(u ∇ v)' = ρv' ∇ u.\n", "\n", "Substituting this expression for the first derivative into the NSE, we get:\n", "\n", "∃(u'_0) such that ∑ρ(du'_0 / dt + ∇ ⋅ v') dV = (u - u₀)(...).\n", "\n", "Taking the limit as time goes to infinity and summing over all fluid particles on a closed surface S (again, this is possible because the flow field v(x,t) is assumed to be conservative for long times), we get:\n", "\n", "0 = ∆p/u.\n", "\n", "**Conclusion**: We have shown that under time-reversal symmetry for incompressible fluids, the Navier-Stokes Equations hold as:\n", "\n", "∇ ⋅ v = 0\n", "∂v/∂t + ρ(∇ (u ∇ v)) = -1/ρ (∇ p).\n", "\n", "This result establishes a beautiful relationship between time-reversal symmetry and conservation laws in fluid dynamics.\n"]}], "source": ["# Ask it again\n", "\n", "response = openai.chat.completions.create(\n", "    model=MODEL,\n", "    messages=messages\n", ")\n", "\n", "answer = response.choices[0].message.content\n", "print(answer)\n"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/markdown": ["The Navier-Stokes Equations (NSE) are a set of nonlinear partial differential equations that describe the motion of fluids. Under time-reversal symmetry, i.e., if you reverse the direction of time, the solution remains unchanged.\n", "\n", "In general, the NSE can be written as:\n", "\n", "∇ ⋅ v = 0\n", "∂v/∂t + v ∇ v = -1/ρ ∇ p\n", "\n", "where v is the velocity field, ρ is the density, and p is the pressure.\n", "\n", "To prove that these equations hold under time-reversal symmetry, we can follow a step-by-step approach:\n", "\n", "**Step 1: Homogeneity**: Suppose you have an incompressible fluid, i.e., ρv = ρ and v · v = 0. If you reverse time, then the density remains constant (ρ ∝ t^(-2)), so we have ρ(∂t/∂t + ∇ ⋅ v) = ∂ρ/∂t.\n", "\n", "Using the product rule and the vector identity for divergence, we can rewrite this as:\n", "\n", "∂ρ/∂t = ∂p/(∇ ⋅ p).\n", "\n", "Since p is a function of v only (because of homogeneity), we have:\n", "\n", "∂p/∂v = 0, which implies that ∂p/∂t = 0.\n", "\n", "**Step 2: Uniqueness**: Suppose there are two solutions to the NSE, u_1 and u_2. If you reverse time, then:\n", "\n", "u_1' = -u_2'\n", "\n", "where \"'\" denotes the inverse of the negative sign. Using the equation v + ∇v = (-1/ρ)∇p, we can rewrite this as:\n", "\n", "∂u_2'/∂t = 0.\n", "\n", "Integrating both sides with respect to time, we get:\n", "\n", "u_2' = u_2\n", "\n", "So, u_2 and u_1 are equivalent under time reversal.\n", "\n", "**Step 3: Conserved charge**: Let's consider a flow field v(x,t) subject to the boundary conditions (<PERSON><PERSON><PERSON> or <PERSON>) at a fixed point x. These boundary conditions imply that there is no flux through the surface of the fluid, so:\n", "\n", "∫_S v · n dS = 0.\n", "\n", "where n is the outward unit normal vector to the surface S bounding the domain D containing the flow field. Since ρv = ρ and v · v = 0 (from time reversal), we have that the total charge Q within the fluid remains conserved:\n", "\n", "∫_D ρ(du/dt + ∇ ⋅ v) dV = Q.\n", "\n", "Since u = du/dt, we can rewrite this as:\n", "\n", "∃Q'_T such that ∑u_i' = -∮v · n dS.\n", "\n", "Taking the limit as time goes to infinity and summing over all fluid particles on a closed surface S (this is possible because the flow field v(x,t) is assumed to be conservative for long times), we get:\n", "\n", "Q_u = -∆p, where p_0 = ∂p/∂v evaluated on the initial condition.\n", "\n", "**Step 4: Time reversal invariance**: Now that we have shown both time homogeneity and uniqueness under time reversal, let's consider what happens to the NSE:\n", "\n", "∇ ⋅ v = ρvu'\n", "∂v/∂t + ∇(u ∇ v) = -1/ρ ∇ p'\n", "\n", "We can swap the order of differentiation with respect to t and evaluate each term separately:\n", "\n", "(u ∇ v)' = ρv' ∇ u.\n", "\n", "Substituting this expression for the first derivative into the NSE, we get:\n", "\n", "∃(u'_0) such that ∑ρ(du'_0 / dt + ∇ ⋅ v') dV = (u - u₀)(...).\n", "\n", "Taking the limit as time goes to infinity and summing over all fluid particles on a closed surface S (again, this is possible because the flow field v(x,t) is assumed to be conservative for long times), we get:\n", "\n", "0 = ∆p/u.\n", "\n", "**Conclusion**: We have shown that under time-reversal symmetry for incompressible fluids, the Navier-Stokes Equations hold as:\n", "\n", "∇ ⋅ v = 0\n", "∂v/∂t + ρ(∇ (u ∇ v)) = -1/ρ (∇ p).\n", "\n", "This result establishes a beautiful relationship between time-reversal symmetry and conservation laws in fluid dynamics."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from IPython.display import Markdown, display\n", "\n", "display(<PERSON><PERSON>(answer))\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Congratulations!\n", "\n", "That was a small, simple step in the direction of Agentic AI, with your new environment!\n", "\n", "Next time things get more interesting..."]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../assets/exercise.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#ff7800;\">Exercise</h2>\n", "            <span style=\"color:#ff7800;\">Now try this commercial application:<br/>\n", "            First ask the LLM to pick a business area that might be worth exploring for an Agentic AI opportunity.<br/>\n", "            Then ask the LLM to present a pain-point in that industry - something challenging that might be ripe for an Agentic solution.<br/>\n", "            Finally have 3 third LLM call propose the Agentic AI solution.\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Business idea: Predictive Modeling and Business Intelligence\n"]}], "source": ["# First create the messages:\n", "\n", "messages = [{\"role\": \"user\", \"content\": \"Pick a business area that might be worth exploring for an agentic AI startup. Respond only with the business area.\"}]\n", "\n", "# Then make the first call:\n", "\n", "response = openai.chat.completions.create(\n", "    model=MODEL,\n", "    messages=messages\n", ")\n", "\n", "# Then read the business idea:\n", "\n", "business_idea = response.choices[0].message.content\n", "\n", "# And repeat!\n", "print(f\"Business idea: {business_idea}\")"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Pain point: \"Implementing predictive analytics models that integrate with existing workflows, yet struggle to effectively translate data into actionable insights for key business stakeholders, resulting in delayed decision-making processes and missed opportunities.\"\n"]}], "source": ["messages = [{\"role\": \"user\", \"content\": \"Present a pain point in the business area of \" + business_idea + \". Respond only with the pain point.\"}]\n", "\n", "response = openai.chat.completions.create(\n", "    model=MODEL,\n", "    messages=messages\n", ")\n", "\n", "pain_point = response.choices[0].message.content\n", "print(f\"Pain point: {pain_point}\")"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Solution: **Solution:**\n", "\n", "1. **Develop a Centralized Data Integration Framework**: Design and implement a standardized framework for integrating predictive analytics models with existing workflows, leveraging APIs, data warehouses, or data lakes to store and process data from various sources.\n", "2. **Use Business-Defined Data Pipelines**: Create custom data pipelines that define the pre-processing, cleaning, and transformation of raw data into a format suitable for model development and deployment.\n", "3. **Utilize Machine Learning Model Selection Platforms**: Leverage platforms like TensorFlow Forge, Gluon AI, or Azure Machine Learning to easily deploy trained models from various programming languages and integrate them with data pipelines.\n", "4. **Implement Interactive Data Storytelling Dashboards**: Develop interactive dashboards that allow business stakeholders to explore predictive analytics insights, drill down into detailed reports, and visualize the impact of their decisions on key metrics.\n", "5. **Develop a Governance Framework for Model Deployment**: Establish clear policies and procedures for model evaluation, monitoring, and retraining, ensuring continuous improvement and scalability.\n", "6. **Train Key Stakeholders in Data Science and Predictive Analytics**: Provide targeted training and education programs to develop skills in data science, predictive analytics, and domain expertise, enabling stakeholders to effectively communicate insights and drive decision-making.\n", "7. **Continuous Feedback Mechanism for Model Improvements**: Establish a continuous feedback loop by incorporating user input, performance metrics, and real-time monitoring into the development process, ensuring high-quality models that meet business needs.\n", "\n", "**Implementation Roadmap:**\n", "\n", "* Months 1-3: Data Integration Framework Development, Business-Defined Data Pipelines Creation\n", "* Months 4-6: Machine Learning Model Selection Platforms Deployment, Model Testing & Evaluation\n", "* Months 7-9: Launch Data Storytelling Dashboards, Governance Framework Development\n", "* Months 10-12: Stakeholder Onboarding Program, Continuous Feedback Loop Establishment\n"]}], "source": ["messages = [{\"role\": \"user\", \"content\": \"Present a solution to the pain point of \" + pain_point + \". Respond only with the solution.\"}]\n", "response = openai.chat.completions.create(\n", "    model=MODEL,\n", "    messages=messages\n", ")\n", "solution = response.choices[0].message.content\n", "print(f\"Solution: {solution}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}