{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["BASED ON Week 1 Day 3 LAB Exercise\n", "\n", "This program evaluates different LLM outputs who are acting as customer service representative and are replying to an irritated customer.\n", "OpenAI 40 mini, Gemini, Deepseek, Groq and Ollama are customer service representatives who respond to the email and OpenAI 3o mini analyzes all the responses and ranks their output based on different parameters."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Start with imports -\n", "import os\n", "import json\n", "from dotenv import load_dotenv\n", "from openai import OpenAI\n", "from anthropic import Anthropic\n", "from IPython.display import Markdown, display"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Always remember to do this!\n", "load_dotenv(override=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Print the key prefixes to help with any debugging\n", "\n", "openai_api_key = os.getenv('OPENAI_API_KEY')\n", "google_api_key = os.getenv('GOOGLE_API_KEY')\n", "deepseek_api_key = os.getenv('DEEPSEEK_API_KEY')\n", "groq_api_key = os.getenv('GROQ_API_KEY')\n", "\n", "if openai_api_key:\n", "    print(f\"OpenAI API Key exists and begins {openai_api_key[:8]}\")\n", "else:\n", "    print(\"OpenAI API Key not set\")\n", "\n", "if google_api_key:\n", "    print(f\"Google API Key exists and begins {google_api_key[:2]}\")\n", "else:\n", "    print(\"Google API Key not set (and this is optional)\")\n", "\n", "if deepseek_api_key:\n", "    print(f\"DeepSeek API Key exists and begins {deepseek_api_key[:3]}\")\n", "else:\n", "    print(\"DeepSeek API Key not set (and this is optional)\")\n", "\n", "if groq_api_key:\n", "    print(f\"Groq API Key exists and begins {groq_api_key[:4]}\")\n", "else:\n", "    print(\"Groq API Key not set (and this is optional)\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["persona = \"You are a customer support representative for a subscription bases software product.\"\n", "email_content = '''Subject: Totally unacceptable experience\n", "\n", "Hi,\n", "\n", "I’ve already written to you twice about this, and still no response. I was charged again this month even after canceling my subscription. This is the third time this has happened.\n", "\n", "Honestly, I’m losing patience. If I don’t get a clear explanation and refund within 24 hours, I’m going to report this on social media and leave negative reviews.\n", "\n", "You’ve seriously messed up here. Fix this now.\n", "\n", "– Jordan\n", "\n", "'''"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["messages = [{\"role\":\"system\", \"content\": persona}]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["request = f\"\"\"A frustrated customer has written in about being repeatedly charged after canceling and threatened to escalate on social media.\n", "Write a calm, empathetic, and professional response that Acknowledges their frustration, Apologizes sincerely,Explains the next steps to resolve the issue\n", "Attempts to de-escalate the situation. Keep the tone respectful and proactive. Do not make excuses or blame the customer.\"\"\"\n", "request += f\" Here is the email : {email_content}]\"\n", "messages.append({\"role\": \"user\", \"content\": request})\n", "print(messages)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["messages"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["competitors = []\n", "answers = []\n", "messages = [{\"role\": \"user\", \"content\": request}]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# The API we know well\n", "openai = OpenAI()\n", "model_name = \"gpt-4o-mini\"\n", "\n", "response = openai.chat.completions.create(model=model_name, messages=messages)\n", "answer = response.choices[0].message.content\n", "\n", "display(<PERSON><PERSON>(answer))\n", "competitors.append(model_name)\n", "answers.append(answer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gemini = OpenAI(api_key=google_api_key, base_url=\"https://generativelanguage.googleapis.com/v1beta/openai/\")\n", "model_name = \"gemini-2.0-flash\"\n", "\n", "response = gemini.chat.completions.create(model=model_name, messages=messages)\n", "answer = response.choices[0].message.content\n", "\n", "display(<PERSON><PERSON>(answer))\n", "competitors.append(model_name)\n", "answers.append(answer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["deepseek = OpenAI(api_key=deepseek_api_key, base_url=\"https://api.deepseek.com/v1\")\n", "model_name = \"deepseek-chat\"\n", "\n", "response = deepseek.chat.completions.create(model=model_name, messages=messages)\n", "answer = response.choices[0].message.content\n", "\n", "display(<PERSON><PERSON>(answer))\n", "competitors.append(model_name)\n", "answers.append(answer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["groq = OpenAI(api_key=groq_api_key, base_url=\"https://api.groq.com/openai/v1\")\n", "model_name = \"llama-3.3-70b-versatile\"\n", "\n", "response = groq.chat.completions.create(model=model_name, messages=messages)\n", "answer = response.choices[0].message.content\n", "\n", "display(<PERSON><PERSON>(answer))\n", "competitors.append(model_name)\n", "answers.append(answer)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!ollama pull llama3.2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["ollama = OpenAI(base_url='http://localhost:11434/v1', api_key='ollama')\n", "model_name = \"llama3.2\"\n", "\n", "response = ollama.chat.completions.create(model=model_name, messages=messages)\n", "answer = response.choices[0].message.content\n", "\n", "display(<PERSON><PERSON>(answer))\n", "competitors.append(model_name)\n", "answers.append(answer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# So where are we?\n", "\n", "print(competitors)\n", "print(answers)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# It's nice to know how to use \"zip\"\n", "for competitor, answer in zip(competitors, answers):\n", "    print(f\"Competitor: {competitor}\\n\\n{answer}\")\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["# Let's bring this together - note the use of \"enumerate\"\n", "\n", "together = \"\"\n", "for index, answer in enumerate(answers):\n", "    together += f\"# Response from competitor {index+1}\\n\\n\"\n", "    together += answer + \"\\n\\n\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(together)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["judge = f\"\"\"You are judging the performance of {len(competitors)} who are customer service representatives in a SaaS based subscription model company.\n", "Each has responded to below grievnace email from the customer:\n", "\n", "{request}\n", "\n", "Evaluate the following customer support reply based on these criteria. Assign a score from 1 (very poor) to 5 (excellent) for each:\n", "\n", "1. Empathy:\n", "Does the message acknowledge the customer’s frustration appropriately and sincerely?\n", "\n", "2. De-escalation:\n", "Does the response effectively calm the customer and reduce the likelihood of social media escalation?\n", "\n", "3. <PERSON>lar<PERSON>:\n", "Is the explanation of next steps clear and specific (e.g., refund process, timeline)?\n", "\n", "4. Professional Tone:\n", "Is the message respectful, calm, and free from defensiveness or blame?\n", "\n", "Provide a one-sentence explanation for each score and a final overall rating with justification.\n", "\n", "Here are the responses from each competitor:\n", "\n", "{together}\n", "\n", "Do not include markdown formatting or code blocks. Also create a table with 3 columnds at the end containing rank, name and one line reason for the rank\"\"\"\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(judge)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["judge_messages = [{\"role\": \"user\", \"content\": judge}]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Judgement time!\n", "\n", "openai = OpenAI()\n", "response = openai.chat.completions.create(\n", "    model=\"o3-mini\",\n", "    messages=judge_messages,\n", ")\n", "results = response.choices[0].message.content\n", "print(results)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(results)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}