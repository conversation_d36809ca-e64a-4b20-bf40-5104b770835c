{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["#### llm_legal_advisor (Parallelization-pattern)\n", "\n", "#### Overview\n", "This module implements a parallel legal document analysis system using multiple AI agents to process legal documents concurrently."]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [], "source": ["# Start with imports \n", "import os\n", "import json\n", "from dotenv import load_dotenv\n", "from openai import OpenAI\n", "from IPython.display import Markdown, display\n", "import concurrent.futures"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [], "source": ["load_dotenv(override=True)\n", "open_api_key = os.getenv(\"OPENAI_API_KEY\")\n", "groq_api_key = os.getenv(\"GROQ_API_KEY\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### Helper Functions\n", "\n", "##### Technical Details\n", "- **Concurrency**: Uses ThreadPoolExecutor for parallel processing\n", "- **API**: Groq API with OpenAI-compatible interface\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### `llm_summarizer`"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [], "source": ["# Summarizes legal documents using AI\n", "def llm_summarizer(document: str) -> str:\n", "    response = OpenAI(api_key=groq_api_key, base_url=\"https://api.groq.com/openai/v1\").chat.completions.create(\n", "        model=\"llama-3.3-70b-versatile\",\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": \"You are a corporate lawyer. Summarize the key points of legal documents clearly.\"},\n", "            {\"role\": \"user\", \"content\": f\"Summarize this document:\\n\\n{document}\"}\n", "        ],\n", "        temperature=0.3,\n", "    )\n", "    return response.choices[0].message.content"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### `llm_evaluate_risks`"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["# Identifies and analyzes legal risks in documents\n", "def llm_evaluate_risks(document: str) -> str:\n", "    response = OpenAI(api_key=groq_api_key, base_url=\"https://api.groq.com/openai/v1\").chat.completions.create(\n", "        model=\"llama-3.3-70b-versatile\",\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": \"You are a corporate lawyer. Identify and explain legal risks in the following document.\"},\n", "            {\"role\": \"user\", \"content\": f\"Analyze the legal risks:\\n\\n{document}\"}\n", "        ],\n", "        temperature=0.3,\n", "    )\n", "    return response.choices[0].message.content"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### `llm_tag_clauses`"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [], "source": ["# Classifies and tags legal clauses by category\n", "def llm_tag_clauses(document: str) -> str:\n", "    response = OpenAI(api_key=groq_api_key, base_url=\"https://api.groq.com/openai/v1\").chat.completions.create(\n", "        model=\"llama-3.3-70b-versatile\",\n", "        messages=[\n", "            {\"role\": \"system\", \"content\": \"You are a legal clause classifier. Tag each clause with relevant legal and compliance categories.\"},\n", "            {\"role\": \"user\", \"content\": f\"Classify and tag clauses in this document:\\n\\n{document}\"}\n", "        ],\n", "        temperature=0.3,\n", "    )\n", "    return response.choices[0].message.content"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### `aggregator`"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [], "source": ["# Organizes and formats multiple AI responses into a structured report\n", "def aggregator(responses: list[str]) -> str:\n", "    sections = {\n", "        \"summary\": \"[Section 1: Summary]\",\n", "        \"risk\": \"[Section 2: Risk Analysis]\",\n", "        \"clauses\": \"[Section 3: Clause Classification & Compliance Tags]\"\n", "    }\n", "\n", "    ordered = {\n", "        \"summary\": None,\n", "        \"risk\": None,\n", "        \"clauses\": None\n", "    }\n", "\n", "    for r in responses:\n", "        content = r.lower()\n", "        if any(keyword in content for keyword in [\"summary\", \"[summary]\"]):\n", "            ordered[\"summary\"] = r\n", "        elif any(keyword in content for keyword in [\"risk\", \"liability\"]):\n", "            ordered[\"risk\"] = r\n", "        else:\n", "            ordered[\"clauses\"] = r\n", "\n", "    report_sections = [\n", "        f\"{sections[key]}\\n{value.strip()}\"\n", "        for key, value in ordered.items() if value\n", "    ]\n", "\n", "    return \"\\n\\n\".join(report_sections)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["##### `coordinator`"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [], "source": ["# Orchestrates parallel execution of all legal analysis agents\n", "def coordinator(document: str) -> str:\n", "    \"\"\"Dispatch document to agents and aggregate results\"\"\"\n", "    agents = [llm_summarizer, llm_evaluate_risks, llm_tag_clauses]\n", "    with concurrent.futures.ThreadPoolExecutor() as executor:\n", "        futures = [executor.submit(agent, document) for agent in agents]\n", "        results = [f.result() for f in concurrent.futures.as_completed(futures)]\n", "    return aggregator(results)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<b>Lets ask our legal corporate advisor</b>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dummy_document = \"\"\"\n", "This agreement is made between ABC Corp and XYZ Ltd. The responsibilities of each party shall be determined as the project progresses.\n", "ABC Corp may terminate the contract at its discretion. No specific provisions are mentioned regarding data protection or compliance with GDPR.\n", "For more information, refer the clauses 10 of the agreement.\n", "\"\"\"\n", "\n", "final_report = coordinator(dummy_document)\n", "print(final_report)\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 2}