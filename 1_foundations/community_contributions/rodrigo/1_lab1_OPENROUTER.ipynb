{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# First let's do an import\n", "from dotenv import load_dotenv\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Next it's time to load the API keys into environment variables\n", "\n", "load_dotenv(override=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check the keys\n", "\n", "import os\n", "openRouter_api_key = os.getenv('OPENROUTER_API_KEY')\n", "\n", "if openRouter_api_key:\n", "    print(f\"OpenRouter API Key exists and begins {openRouter_api_key[:8]}\")\n", "else:\n", "    print(\"OpenRouter API Key not set - please head to the troubleshooting guide in the setup folder\")\n", "    \n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "# Set the model you want to use\n", "#MODEL = \"openai/gpt-4.1-nano\"\n", "MODEL = \"meta-llama/llama-3.3-8b-instruct:free\"\n", "#MODEL = \"openai/gpt-4.1-mini\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["chatHistory = []\n", "# This is a list that will hold the chat history"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Instead of using the OpenAI API, here I will use the OpenRouter API\n", "# This is a method that can be reused to chat with the OpenRouter API\n", "def chat<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(prompt):\n", "\n", "    # here add the prommpt to the chat history\n", "    chatHistory.append({\"role\": \"user\", \"content\": prompt})\n", "\n", "    # specify the URL and headers for the OpenRouter API\n", "    url = \"https://openrouter.ai/api/v1/chat/completions\"\n", "    \n", "    headers = {\n", "        \"Authorization\": f\"Bearer {openRouter_api_key}\",\n", "        \"Content-Type\": \"application/json\"\n", "    }\n", "\n", "    payload = {\n", "        \"model\": MODEL,\n", "        \"messages\":chatHistory\n", "    }\n", "\n", "    # make the POST request to the OpenRouter API\n", "    response = requests.post(url, headers=headers, json=payload)\n", "\n", "    # check if the response is successful\n", "    # and return the response content\n", "    if response.status_code == 200:\n", "        print(f\"Row Response:\\n{response.json()}\")\n", "\n", "        assistantResponse = response.json()['choices'][0]['message']['content']\n", "        chatHistory.append({\"role\": \"assistant\", \"content\": assistant<PERSON><PERSON><PERSON>nse})\n", "        return f\"LLM response:\\n{assistantResponse}\"\n", "    \n", "    else:\n", "        raise Exception(f\"Error: {response.status_code},\\n {response.text}\")\n", "    \n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# message to use with chatWithOpenRouter function\n", "messages = \"What is 2+2?\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Now let's make a call to the chatWithOpenRouter function\n", "response = chatWithOpenRouter(messages)\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["question = \"Please propose a hard, challenging question to assess someone's IQ. Respond only with the question.\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Trying with a question\n", "response = chatWithOpen<PERSON><PERSON>er(question)\n", "print(response)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["message = response\n", "answer = chatWithOpenRouter(\"Solve the question: \"+message)\n", "print(answer)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Congratulations!\n", "\n", "That was a small, simple step in the direction of Agentic AI, with your new environment!\n", "\n", "Next time things get more interesting..."]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../../../assets/exercise.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#ff7800;\">Exercise</h2>\n", "            <span style=\"color:#ff7800;\">Now try this commercial application:<br/>\n", "            First ask the LLM to pick a business area that might be worth exploring for an Agentic AI opportunity.<br/>\n", "            Then ask the LLM to present a pain-point in that industry - something challenging that might be ripe for an Agentic solution.<br/>\n", "            Finally have 3 third LLM call propose the Agentic AI solution.\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# First create the messages:\n", "exerciseMessage = \"Tell me about a business area that migth be worth exploring for an Agentic AI apportinitu\"\n", "\n", "# Then make the first call:\n", "response = chatWith<PERSON><PERSON><PERSON><PERSON>er(exerciseMessage)\n", "\n", "# Then read the business idea:\n", "business_idea = response\n", "print(business_idea)\n", "\n", "# And repeat!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# First create the messages:\n", "exerciseMessage = \"Present a pain-point in that industry - something challenging that might be ripe for an Agentic solution.\"\n", "\n", "# Then make the first call:\n", "response = chatWith<PERSON><PERSON><PERSON><PERSON>er(exerciseMessage)\n", "\n", "# Then read the business idea:\n", "business_idea = response\n", "print(business_idea)\n", "\n", "# And repeat!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(len(chatHistory))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "UV_Py_3.12", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 2}