{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Welcome to the Second Lab - Week 1, Day 3\n", "### Edited version (<PERSON><PERSON><PERSON>)\n", "\n", "Today we will work with lots of models! This is a way to get comfortable with APIs."]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../../../assets/stop.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#ff7800;\">Important point - please read</h2>\n", "            <span style=\"color:#ff7800;\">The way I collaborate with you may be different to other courses you've taken. I prefer not to type code while you watch. Rather, I execute Jupyter Labs, like this, and give you an intuition for what's going on. My suggestion is that you carefully execute this yourself, <b>after</b> watching the lecture. Add print statements to understand what's going on, and then come up with your own variations.<br/><br/>If you have time, I'd love it if you submit a PR for changes in the community_contributions folder - instructions in the resources. Also, if you have a Github account, use this to showcase your variations. Not only is this essential practice, but it demonstrates your skills to others, including perhaps future clients or employers...\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In this case "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Start with imports - ask ChatGPT to explain any package that you don't know\n", "import json\n", "from zroddeUtils import llmModels, openRouterUtils\n", "from IPython.display import display, Markdown"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["request = \"Please come up with a challenging, nuanced question that I can ask a number of LLMs to evaluate their intelligence. \"\n", "request += \"Answer only with the question, no explanation.\"\n", "prompt = request\n", "model = llmModels.free_mistral_Small_31_24B"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["llmQuestion = openRouterUtils.getOpenrouterResponse(model, prompt)\n", "print(llmQuestion)\n", "#openRouterUtils.clearChatHistory()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["competitors = {} # In this dictionary, we will store the responses from each LLM\n", "                 # competitors[model] = llmResponse"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# In this case I need to delete the history because I will to ask the same question to different models\n", "openRouterUtils.clearChatHistory()\n", "\n", "# Set the model name which I'll use to get a response\n", "#model_name = llmModels.free_gemini_20_flash_exp\n", "model_name = llmModels.free_meta_Llama_4_Maverick\n", "\n", "# Use the same method to interact with the LLM as before\n", "llmResponse = openRouterUtils.getOpenrouterResponse(model_name, llmQuestion)\n", "\n", "# Display the response in a Markdown format\n", "display(Markdown(llmResponse))\n", "\n", "# Store the response in the competitors dictionary\n", "competitors[model_name] = {\"Number\":len(competitors)+1, \"Response\":llmResponse}\n", "\n", "# The competitors dictionary stores each model's response using the model name as the key.\n", "# The value is another dictionary with the model's assigned number and its response."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# In this case I need to delete the history because I will to ask the same question to different models\n", "openRouterUtils.clearChatHistory()\n", "\n", "# Set the model name which I'll use to get a response\n", "model_name = llmModels.free_nous_Hermes_3_Mistral_24B\n", "\n", "# Use the same method to interact with the LLM as before\n", "llmResponse = openRouterUtils.getOpenrouterResponse(model_name, llmQuestion)\n", "\n", "# Display the response in a Markdown format\n", "display(Markdown(llmResponse))\n", "\n", "# Store the response in the competitors dictionary\n", "competitors[model_name] = {\"Number\":len(competitors)+1, \"Response\":llmResponse}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# In this case I need to delete the history because I will to ask the same question to different models\n", "openRouterUtils.clearChatHistory()\n", "\n", "# Set the model name which I'll use to get a response\n", "model_name = llmModels.free_deepSeek_V3_Base\n", "\n", "# Use the same method to interact with the LLM as before\n", "llmResponse = openRouterUtils.getOpenrouterResponse(model_name, llmQuestion)\n", "\n", "# Display the response in a Markdown format\n", "display(Markdown(llmResponse))\n", "\n", "# Store the response in the competitors dictionary\n", "competitors[model_name] = {\"Number\":len(competitors)+1, \"Response\":llmResponse}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# In this case I need to delete the history because I will to ask the same question to different models\n", "openRouterUtils.clearChatHistory()\n", "\n", "# Set the model name which I'll use to get a response\n", "# Be careful with this model. Gemini 2.0  flash is a free model,\n", "# but some times it is not available and you will get an error.\n", "model_name = llmModels.free_gemini_20_flash_exp\n", "\n", "# Use the same method to interact with the LLM as before\n", "llmResponse = openRouterUtils.getOpenrouterResponse(model_name, llmQuestion)\n", "\n", "# Display the response in a Markdown format\n", "display(Markdown(llmResponse))\n", "\n", "# Store the response in the competitors dictionary\n", "competitors[model_name] = {\"Number\":len(competitors)+1, \"Response\":llmResponse}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# In this case I need to delete the history because I will to ask the same question to different models\n", "openRouterUtils.clearChatHistory()\n", "\n", "# Set the model name which I'll use to get a response\n", "model_name = llmModels.Gpt_41_nano\n", "\n", "# Use the same method to interact with the LLM as before\n", "llmResponse = openRouterUtils.getOpenrouterResponse(model_name, llmQuestion)\n", "\n", "# Display the response in a Markdown format\n", "display(Markdown(llmResponse))\n", "\n", "# Store the response in the competitors dictionary\n", "competitors[model_name] = {\"Number\":len(competitors)+1, \"Response\":llmResponse}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Loop through the competitors dictionary and print each model's name and its response,\n", "# separated by a line for readability. Finally, print the total number of competitors.\n", "for k, v in competitors.items():\n", "    print(f\"{k} \\n {v}\\n***********************************\\n\")\n", "\n", "print(len(competitors))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["judge = f\"\"\"You are judging a competition between {len(competitors)} competitors.\n", "Each model has been given this question:\n", "\n", "{llmQuestion}\n", "You will get a dictionary coled \"competitors\" with the name, number and response of each competitor. \n", "Your job is to evaluate each response for clarity and strength of argument, and rank them in order of best to worst.\n", "Respond with JSON, and only JSON, with the following format:\n", "{{\"results\": [\"best competitor number\", \"second best competitor number\", \"third best competitor number\", ...]}}\n", "\n", "Here are the responses from each competitor:\n", "\n", "{competitors}\n", "\n", "Do not base your evaluation on the model name, but only on the content of the responses.\n", "\n", "Now respond with the JSON with the ranked order of the competitors, nothing else. Do not include markdown formatting or code blocks.\"\"\"\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(judge)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["openRouterUtils.chatWithOpenRouter(llmModels.<PERSON>_37_sonnet, judge)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["prompt = \"Give me a breif argumentation about why you put them in this order.\"\n", "openRouterUtils.chatWithOpenRouter(llmModels.Claude_37_sonnet, prompt)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../../../assets/exercise.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#ff7800;\">Exercise</h2>\n", "            <span style=\"color:#ff7800;\">Which pattern(s) did this use? Try updating this to add another Agentic design pattern.\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../../../assets/business.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#00bfff;\">Commercial implications</h2>\n", "            <span style=\"color:#00bfff;\">These kinds of patterns - to send a task to multiple models, and evaluate results,\n", "            and common where you need to improve the quality of your LLM response. This approach can be universally applied\n", "            to business projects where accuracy is critical.\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "UV_Py_3.12", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 2}