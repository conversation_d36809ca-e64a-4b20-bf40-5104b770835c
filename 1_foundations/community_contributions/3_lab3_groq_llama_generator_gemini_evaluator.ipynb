{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Chat app with LinkedIn Profile Information - Groq LLama as Generator and <PERSON> as evaluator\n"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [], "source": ["# If you don't know what any of these packages do - you can always ask ChatGPT for a guide!\n", "\n", "from dotenv import load_dotenv\n", "from openai import OpenAI\n", "from pypdf import PdfReader\n", "from groq import Groq\n", "import gradio as gr"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [], "source": ["load_dotenv(override=True)\n", "groq = Groq()"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [], "source": ["reader = PdfReader(\"me/My_LinkedIn.pdf\")\n", "linkedin = \"\"\n", "for page in reader.pages:\n", "    text = page.extract_text()\n", "    if text:\n", "        linkedin += text"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(linkedin)"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [], "source": ["with open(\"me/summary.txt\", \"r\", encoding=\"utf-8\") as f:\n", "    summary = f.read()"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [], "source": ["name = \"<PERSON><PERSON><PERSON><PERSON>rama<PERSON>\""]}, {"cell_type": "code", "execution_count": 63, "metadata": {}, "outputs": [], "source": ["system_prompt = f\"You are acting as {name}. You are answering questions on {name}'s website, \\\n", "particularly questions related to {name}'s career, background, skills and experience. \\\n", "Your responsibility is to represent {name} for interactions on the website as faithfully as possible. \\\n", "You are given a summary of {name}'s background and LinkedIn profile which you can use to answer questions. \\\n", "Be professional and engaging, as if talking to a potential client or future employer who came across the website. \\\n", "If you don't know the answer, say so.\"\n", "\n", "system_prompt += f\"\\n\\n## Summary:\\n{summary}\\n\\n## LinkedIn Profile:\\n{linkedin}\\n\\n\"\n", "system_prompt += f\"With this context, please chat with the user, always staying in character as {name}.\"\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["system_prompt"]}, {"cell_type": "code", "execution_count": 65, "metadata": {}, "outputs": [], "source": ["def chat(message, history):\n", "    # Below line is to remove the metadata and options from the history\n", "    history = [{k: v for k, v in item.items() if k not in ('metadata', 'options')} for item in history]\n", "    messages = [{\"role\": \"system\", \"content\": system_prompt}] + history + [{\"role\": \"user\", \"content\": message}]\n", "    response = groq.chat.completions.create(model=\"llama-3.3-70b-versatile\", messages=messages)\n", "    return response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gr.ChatInterface(chat, type=\"messages\").launch()"]}, {"cell_type": "code", "execution_count": 67, "metadata": {}, "outputs": [], "source": ["# Create a Pydantic model for the Evaluation\n", "\n", "from pydantic import BaseModel\n", "\n", "class Evaluation(BaseModel):\n", "    is_acceptable: bool\n", "    feedback: str\n"]}, {"cell_type": "code", "execution_count": 69, "metadata": {}, "outputs": [], "source": ["evaluator_system_prompt = f\"You are an evaluator that decides whether a response to a question is acceptable. \\\n", "You are provided with a conversation between a User and an Agent. Your task is to decide whether the Agent's latest response is acceptable quality. \\\n", "The Agent is playing the role of {name} and is representing {name} on their website. \\\n", "The Agent has been instructed to be professional and engaging, as if talking to a potential client or future employer who came across the website. \\\n", "The Agent has been provided with context on {name} in the form of their summary and LinkedIn details. Here's the information:\"\n", "\n", "evaluator_system_prompt += f\"\\n\\n## Summary:\\n{summary}\\n\\n## LinkedIn Profile:\\n{linkedin}\\n\\n\"\n", "evaluator_system_prompt += f\"With this context, please evaluate the latest response, replying with whether the response is acceptable and your feedback.\""]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [], "source": ["def evaluator_user_prompt(reply, message, history):\n", "    user_prompt = f\"Here's the conversation between the User and the Agent: \\n\\n{history}\\n\\n\"\n", "    user_prompt += f\"Here's the latest message from the User: \\n\\n{message}\\n\\n\"\n", "    user_prompt += f\"Here's the latest response from the Agent: \\n\\n{reply}\\n\\n\"\n", "    user_prompt += f\"Please evaluate the response, replying with whether it is acceptable and your feedback.\"\n", "    return user_prompt"]}, {"cell_type": "code", "execution_count": 71, "metadata": {}, "outputs": [], "source": ["import os\n", "gemini = OpenAI(\n", "    api_key=os.getenv(\"GOOGLE_API_KEY\"), \n", "    base_url=\"https://generativelanguage.googleapis.com/v1beta/openai/\"\n", ")"]}, {"cell_type": "code", "execution_count": 72, "metadata": {}, "outputs": [], "source": ["def evaluate(reply, message, history) -> Evaluation:\n", "\n", "    messages = [{\"role\": \"system\", \"content\": evaluator_system_prompt}] + [{\"role\": \"user\", \"content\": evaluator_user_prompt(reply, message, history)}]\n", "    response = gemini.beta.chat.completions.parse(model=\"gemini-2.0-flash\", messages=messages, response_format=Evaluation)\n", "    return response.choices[0].message.parsed"]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [], "source": ["def rerun(reply, message, history, feedback):\n", "    # Below line is to remove the metadata and options from the history\n", "    history = [{k: v for k, v in item.items() if k not in ('metadata', 'options')} for item in history]\n", "    updated_system_prompt = system_prompt + f\"\\n\\n## Previous answer rejected\\nYou just tried to reply, but the quality control rejected your reply\\n\"\n", "    updated_system_prompt += f\"## Your attempted answer:\\n{reply}\\n\\n\"\n", "    updated_system_prompt += f\"## Reason for rejection:\\n{feedback}\\n\\n\"\n", "    messages = [{\"role\": \"system\", \"content\": updated_system_prompt}] + history + [{\"role\": \"user\", \"content\": message}]\n", "    response = groq.chat.completions.create(model=\"llama-3.3-70b-versatile\", messages=messages)\n", "    return response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": 74, "metadata": {}, "outputs": [], "source": ["def chat(message, history):\n", "    if \"personal\" in message:\n", "        system = system_prompt + \"\\n\\nEverything in your reply needs to be in Gen Z language - \\\n", "              it is mandatory that you respond only and entirely in Gen Z language\"\n", "    else:\n", "        system = system_prompt\n", "    # Below line is to remove the metadata and options from the history\n", "    history = [{k: v for k, v in item.items() if k not in ('metadata', 'options')} for item in history]\n", "    messages = [{\"role\": \"system\", \"content\": system}] + history + [{\"role\": \"user\", \"content\": message}]\n", "    response = groq.chat.completions.create(model=\"llama-3.3-70b-versatile\", messages=messages)\n", "    reply =response.choices[0].message.content\n", "\n", "    evaluation = evaluate(reply, message, history)\n", "    \n", "    if evaluation.is_acceptable:\n", "        print(\"Passed evaluation - returning reply\")\n", "    else:\n", "        print(\"Failed evaluation - retrying\")\n", "        print(evaluation.feedback)\n", "        reply = rerun(reply, message, history, evaluation.feedback)       \n", "    return reply"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gr.ChatInterface(chat, type=\"messages\").launch()"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 2}