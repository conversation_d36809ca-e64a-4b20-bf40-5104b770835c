{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Six Thinking Hats Simulator\n", "\n", "## Objective\n", "This notebook implements a simulator of the Six Thinking Hats technique to evaluate and improve technological solutions. The simulator will:\n", "\n", "1. Use an LLM to generate an initial technological solution idea for a specific daily task in a company.\n", "2. Apply the Six Thinking Hats methodology to analyze and improve the proposed solution.\n", "3. Provide a comprehensive evaluation from different perspectives.\n", "\n", "## About the Six Thinking Hats Technique\n", "\n", "The Six Thinking Hats is a powerful technique developed by <PERSON> that helps people look at problems and decisions from different perspectives. Each \"hat\" represents a different thinking approach:\n", "\n", "- **White Hat (Facts):** Focuses on available information, facts, and data.\n", "- **Red Hat (Feelings):** Represents emotions, intuition, and gut feelings.\n", "- **<PERSON> (Critical):** Identifies potential problems, risks, and negative aspects.\n", "- **Yellow Hat (Positive):** Looks for benefits, opportunities, and positive aspects.\n", "- **<PERSON> (Creative):** Encourages new ideas, alternatives, and possibilities.\n", "- **<PERSON> Hat (Process):** Manages the thinking process and ensures all perspectives are considered.\n", "\n", "In this simulator, we'll use these different perspectives to thoroughly evaluate and improve technological solutions proposed by an LLM."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import json\n", "from dotenv import load_dotenv\n", "from openai import OpenAI\n", "from anthropic import Anthropic\n", "from IPython.display import Markdown, display"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["load_dotenv(override=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Print the key prefixes to help with any debugging\n", "\n", "openai_api_key = os.getenv('OPENAI_API_KEY')\n", "anthropic_api_key = os.getenv('ANTHROPIC_API_KEY')\n", "google_api_key = os.getenv('GOOGLE_API_KEY')\n", "deepseek_api_key = os.getenv('DEEPSEEK_API_KEY')\n", "groq_api_key = os.getenv('GROQ_API_KEY')\n", "\n", "if openai_api_key:\n", "    print(f\"OpenAI API Key exists and begins {openai_api_key[:8]}\")\n", "else:\n", "    print(\"OpenAI API Key not set\")\n", "    \n", "if anthropic_api_key:\n", "    print(f\"Anthropic API Key exists and begins {anthropic_api_key[:7]}\")\n", "else:\n", "    print(\"Anthropic API Key not set\")\n", "\n", "if google_api_key:\n", "    print(f\"Google API Key exists and begins {google_api_key[:2]}\")\n", "else:\n", "    print(\"Google API Key not set\")\n", "\n", "if deepseek_api_key:\n", "    print(f\"DeepSeek API Key exists and begins {deepseek_api_key[:3]}\")\n", "else:\n", "    print(\"DeepSeek API Key not set\")\n", "\n", "if groq_api_key:\n", "    print(f\"Groq API Key exists and begins {groq_api_key[:4]}\")\n", "else:\n", "    print(\"Groq API Key not set\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["request = \"Generate a technological solution to solve a specific workplace challenge. Choose an employee role, in a specific industry, and identify a time-consuming or error-prone daily task they face. Then, create an innovative yet practical technological solution that addresses this challenge. Include what technologies it uses (AI, automation, etc.), how it integrates with existing systems, its key benefits, and basic implementation requirements. Keep your solution realistic with current technology. \"\n", "request += \"Answer only with the question, no explanation.\"\n", "messages = [{\"role\": \"user\", \"content\": request}]\n", "\n", "openai = OpenAI()\n", "response = openai.chat.completions.create(\n", "    model=\"gpt-4o-mini\",\n", "    messages=messages,\n", ")\n", "question = response.choices[0].message.content\n", "print(question)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["validation_prompt = f\"\"\"Validate and improve the following technological solution. For each iteration, check if the solution meets these criteria:\n", "\n", "1. Clarity:\n", "   - Is the problem clearly defined?\n", "   - Is the solution clearly explained?\n", "   - Are the technical components well-described?\n", "\n", "2. Specificity:\n", "   - Are there specific examples or use cases?\n", "   - Are the technologies and tools specifically named?\n", "   - Are the implementation steps detailed?\n", "\n", "3. Context:\n", "   - Is the industry/company context clear?\n", "   - Are the user roles and needs well-defined?\n", "   - Is the current workflow/problem well-described?\n", "\n", "4. Constraints:\n", "   - Are there clear technical limitations?\n", "   - Are there budget/time constraints mentioned?\n", "   - Are there integration requirements specified?\n", "\n", "If any of these criteria are not met, improve the solution by:\n", "1. Adding missing details\n", "2. Clarifying ambiguous points\n", "3. Providing more specific examples\n", "4. Including relevant constraints\n", "\n", "Here is the technological solution to validate and improve:\n", "{question} \n", "Provide an improved version that addresses any missing or unclear aspects. If this is the 5th iteration, return the final improved version without further changes.\n", "\n", "Response only with the Improved Solution:\n", "[Your improved solution here]\"\"\"\n", "\n", "messages = [{\"role\": \"user\", \"content\": validation_prompt}]\n", "\n", "response = openai.chat.completions.create(model=\"gpt-4o\", messages=messages)\n", "question = response.choices[0].message.content\n", "\n", "display(Markdown(question))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "In this section, we will ask each AI model to analyze a technological solution using the Six Thinking Hats methodology. Each model will:\n", "\n", "1. First generate a technological solution for a workplace challenge\n", "2. Then analyze that solution using each of the Six Thinking Hats\n", "\n", "Each model will provide:\n", "1. An initial technological solution\n", "2. A structured analysis using all six thinking hats\n", "3. A final recommendation based on the comprehensive analysis\n", "\n", "This approach will allow us to:\n", "- Compare how different models apply the Six Thinking Hats methodology\n", "- Identify patterns and differences in their analytical approaches\n", "- Gather diverse perspectives on the same solution\n", "- Create a rich, multi-faceted evaluation of each proposed technological solution\n", "\n", "The responses will be collected and displayed below, showing how each model applies the Six Thinking Hats methodology to evaluate and improve the proposed solutions."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["models = []\n", "answers = []\n", "combined_question = f\" Analyze the technological solution prposed in {question} using the Six Thinking Hats methodology. For each hat, provide a detailed analysis. Finally, provide a comprehensive recommendation based on all the above analyses.\"\n", "messages = [{\"role\": \"user\", \"content\": combined_question}]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# GPT thinking process\n", "\n", "model_name = \"gpt-4o\"\n", "\n", "\n", "response = openai.chat.completions.create(model=model_name, messages=messages)\n", "answer = response.choices[0].message.content\n", "\n", "display(<PERSON><PERSON>(answer))\n", "models.append(model_name)\n", "answers.append(answer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Claude thinking process\n", "\n", "model_name = \"claude-3-7-sonnet-latest\"\n", "\n", "claude = Anthropic()\n", "response = claude.messages.create(model=model_name, messages=messages, max_tokens=1000)\n", "answer = response.content[0].text\n", "\n", "display(<PERSON><PERSON>(answer))\n", "models.append(model_name)\n", "answers.append(answer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Gemini thinking process\n", "\n", "gemini = OpenAI(api_key=google_api_key, base_url=\"https://generativelanguage.googleapis.com/v1beta/openai/\")\n", "model_name = \"gemini-2.0-flash\"\n", "\n", "response = gemini.chat.completions.create(model=model_name, messages=messages)\n", "answer = response.choices[0].message.content\n", "\n", "display(<PERSON><PERSON>(answer))\n", "models.append(model_name)\n", "answers.append(answer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Deepseek thinking process\n", "\n", "deepseek = OpenAI(api_key=deepseek_api_key, base_url=\"https://api.deepseek.com/v1\")\n", "model_name = \"deepseek-chat\"\n", "\n", "response = deepseek.chat.completions.create(model=model_name, messages=messages)\n", "answer = response.choices[0].message.content\n", "\n", "display(<PERSON><PERSON>(answer))\n", "models.append(model_name)\n", "answers.append(answer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Groq thinking process\n", "\n", "groq = OpenAI(api_key=groq_api_key, base_url=\"https://api.groq.com/openai/v1\")\n", "model_name = \"llama-3.3-70b-versatile\"\n", "\n", "response = groq.chat.completions.create(model=model_name, messages=messages)\n", "answer = response.choices[0].message.content\n", "\n", "display(<PERSON><PERSON>(answer))\n", "models.append(model_name)\n", "answers.append(answer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!ollama pull llama3.2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ollama thinking process\n", "\n", "ollama = OpenAI(base_url='http://localhost:11434/v1', api_key='ollama')\n", "model_name = \"llama3.2\"\n", "\n", "response = ollama.chat.completions.create(model=model_name, messages=messages)\n", "answer = response.choices[0].message.content\n", "\n", "display(<PERSON><PERSON>(answer))\n", "models.append(model_name)\n", "answers.append(answer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for model, answer in zip(models, answers):\n", "    print(f\"Model: {model}\\n\\n{answer}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Next Step: Solution Synthesis and Enhancement\n", "\n", "**Best Recommendation Selection and Extended Solution Development**\n", "\n", "After applying the Six Thinking Hats analysis to evaluate the initial technological solution from multiple perspectives, the simulator will:\n", "\n", "1. **Synthesize Analysis Results**: Compile insights from all six thinking perspectives (White, <PERSON>, Black, Yellow, Green, and Blue hats) to identify the most compelling recommendations and improvements.\n", "\n", "2. **Select Optimal Recommendation**: Using a weighted evaluation system that considers feasibility, impact, and alignment with organizational goals, the simulator will identify and present the single best recommendation that emerged from the Six Thinking Hats analysis.\n", "\n", "3. **Generate Extended Solution**: Building upon the selected best recommendation, the simulator will create a comprehensive, enhanced version of the original technological solution that incorporates:\n", "  - Key insights from the critical analysis (Black Hat)\n", "  - Positive opportunities identified (Yellow Hat)\n", "  - Creative alternatives and innovations (Green Hat)\n", "  - Factual considerations and data requirements (White Hat)\n", "  - User experience and emotional factors (Red Hat)\n", "\n", "4. **Multi-Model Enhancement**: To further strengthen the solution, the simulator will leverage additional AI models or perspectives to provide supplementary recommendations that complement the Six Thinking Hats analysis, offering a more robust and well-rounded final technological solution.\n", "\n", "This step transforms the analytical insights into actionable improvements, delivering a refined solution that has been thoroughly evaluated and enhanced through structured critical thinking."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["together = \"\"\n", "for index, answer in enumerate(answers):\n", "    together += f\"# Response from model {index+1}\\n\\n\"\n", "    together += answer + \"\\n\\n\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from IPython.display import Markdown, display\n", "import re\n", "\n", "print(f\"Each model has been given this technological solution to analyze: {question}\")\n", "\n", "# First, get the best individual response\n", "judge_prompt = f\"\"\"\n", "    You are judging the quality of {len(models)} responses.\n", "    Evaluate each response based on:\n", "    1. Clarity and coherence\n", "    2. <PERSON><PERSON>h of analysis\n", "    3. Practicality of recommendations\n", "    4. Originality of insights\n", "    \n", "    Rank the responses from best to worst.\n", "    Respond with the model index of the best response, nothing else.\n", "    \n", "    Here are the responses:\n", "    {answers}\n", "    \"\"\"\n", "    \n", "# Get the best response\n", "judge_response = openai.chat.completions.create(\n", "    model=\"o3-mini\",\n", "    messages=[{\"role\": \"user\", \"content\": judge_prompt}]\n", ")\n", "best_response = judge_response.choices[0].message.content\n", "\n", "print(f\"Best Response's Model: {models[int(best_response)]}\")\n", "\n", "synthesis_prompt = f\"\"\"\n", "    Here is the best response's model index from the judge:\n", "\n", "    {best_response}\n", "\n", "    And here are the responses from all the models:\n", "\n", "    {together}\n", "\n", "    Synthesize the responses from the non-best models into one comprehensive answer that:\n", "    1. Captures the best insights from each response that could add value to the best response from the judge\n", "    2. Resolves any contradictions between responses before extending the best response\n", "    3. Presents a clear and coherent final answer that is a comprehensive extension of the best response from the judge\n", "    4. Maintains the same format as the original best response from the judge\n", "    5. Compiles all additional recommendations mentioned by all models\n", "\n", "    Show the best response {answers[int(best_response)]} and then your synthesized response specifying which are additional recommendations to the best response:\n", "    \"\"\"\n", "\n", "# Get the synthesized response\n", "synthesis_response = claude.messages.create(\n", "    model=\"claude-3-7-sonnet-latest\",\n", "    messages=[{\"role\": \"user\", \"content\": synthesis_prompt}],\n", "    max_tokens=10000\n", ")\n", "synthesized_answer = synthesis_response.content[0].text\n", "\n", "converted_answer = re.sub(r'\\\\[\\[\\]]', '$$', synthesized_answer)\n", "display(Markdown(converted_answer))"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 2}