{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Start with imports - ask ChatGPT to explain any package that you don't know\n", "\n", "import os\n", "import json\n", "from dotenv import load_dotenv\n", "from openai import OpenAI\n", "from anthropic import Anthropic\n", "from IPython.display import Markdown, display"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<b>Load and check your API keys</b>\n", "</br>\n", "<b>- - - - - - - - - - - - - - - -</b>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Always remember to do this!\n", "load_dotenv(override=True)\n", "\n", "# Function to check and display API key status\n", "def check_api_key(key_name):\n", "    key = os.getenv(key_name)\n", "    \n", "    if key:\n", "        # Always show the first 7 characters of the key\n", "        print(f\"✓ {key_name} API Key exists and begins... ({key[:7]})\")\n", "        return True\n", "    else:\n", "        print(f\"⚠️ {key_name} API Key not set\")\n", "        return False\n", "\n", "# Check each API key (the function now returns True or False)\n", "has_openai = check_api_key('OPENAI_API_KEY')\n", "has_anthropic = check_api_key('ANTHROPIC_API_KEY')\n", "has_google = check_api_key('GOOGLE_API_KEY')\n", "has_deepseek = check_api_key('DEEPSEEK_API_KEY')\n", "has_groq = check_api_key('GROQ_API_KEY')"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "html"}}, "source": ["<b>Input for travel planner</b></br>\n", "Describe yourself, your travel companions, and the destination you plan to visit.\n", "</br>\n", "<b>- - - - - - - - - - - - - - - -</b>"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Provide a description of you or your family. Age, interests, etc.\n", "person_description = \"family with a 3 year-old\"\n", "# Provide the name of the specific destination or attraction and country\n", "destination = \"Belgium, Brussels\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["<b>- - - - - - - - - - - - - - - -</b>"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["prompt = f\"\"\"\n", "Given the following description of a person or family:\n", "{person_description}\n", "\n", "And the requested travel destination or attraction:\n", "{destination}\n", "\n", "Provide a concise response including:\n", "\n", "1. Fit rating (1-10) specifically for this person or family.\n", "2. One compelling positive reason why this destination suits them.\n", "3. One notable drawback they should consider before visiting.\n", "4. One important additional aspect to consider related to this location.\n", "5. Suggest a few additional places that might also be of interest to them that are very close to the destination.\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def run_prompt_on_available_models(prompt):\n", "    \"\"\"\n", "    Run a prompt on all available AI models based on API keys.\n", "    Continues processing even if some models fail.\n", "    \"\"\"\n", "    results = {}\n", "    api_response = [{\"role\": \"user\", \"content\": prompt}]\n", "    \n", "    # OpenAI\n", "    if check_api_key('OPENAI_API_KEY'):\n", "        try:\n", "            model_name = \"gpt-4o-mini\"\n", "            openai_client = OpenAI()\n", "            response = openai_client.chat.completions.create(model=model_name, messages=api_response)\n", "            results[model_name] = response.choices[0].message.content\n", "            print(f\"✓ Got response from {model_name}\")\n", "        except Exception as e:\n", "            print(f\"⚠️ Error with {model_name}: {str(e)}\")\n", "            # Continue with other models\n", "    \n", "    # Anthropic\n", "    if check_api_key('ANTHROPIC_API_KEY'):\n", "        try:\n", "            model_name = \"claude-3-7-sonnet-latest\"\n", "            # Create new client each time\n", "            claude = Anthropic()\n", "            \n", "            # Use messages directly \n", "            response = claude.messages.create(\n", "                model=model_name,\n", "                messages=[{\"role\": \"user\", \"content\": prompt}],\n", "                max_tokens=1000\n", "            )\n", "            results[model_name] = response.content[0].text\n", "            print(f\"✓ Got response from {model_name}\")\n", "        except Exception as e:\n", "            print(f\"⚠️ Error with {model_name}: {str(e)}\")\n", "            # Continue with other models\n", "    \n", "    # Google\n", "    if check_api_key('GOOGLE_API_KEY'):\n", "        try:\n", "            model_name = \"gemini-2.0-flash\"\n", "            google_api_key = os.getenv('GOOGLE_API_KEY')\n", "            gemini = OpenAI(api_key=google_api_key, base_url=\"https://generativelanguage.googleapis.com/v1beta/openai/\")\n", "            response = gemini.chat.completions.create(model=model_name, messages=api_response)\n", "            results[model_name] = response.choices[0].message.content\n", "            print(f\"✓ Got response from {model_name}\")\n", "        except Exception as e:\n", "            print(f\"⚠️ Error with {model_name}: {str(e)}\")\n", "            # Continue with other models\n", "    \n", "    # DeepSeek\n", "    if check_api_key('DEEPSEEK_API_KEY'):\n", "        try:\n", "            model_name = \"deepseek-chat\"\n", "            deepseek_api_key = os.getenv('DEEPSEEK_API_KEY')\n", "            deepseek = OpenAI(api_key=deepseek_api_key, base_url=\"https://api.deepseek.com/v1\")\n", "            response = deepseek.chat.completions.create(model=model_name, messages=api_response)\n", "            results[model_name] = response.choices[0].message.content\n", "            print(f\"✓ Got response from {model_name}\")\n", "        except Exception as e:\n", "            print(f\"⚠️ Error with {model_name}: {str(e)}\")\n", "            # Continue with other models\n", "    \n", "    # Groq\n", "    if check_api_key('GROQ_API_KEY'):\n", "        try:\n", "            model_name = \"llama-3.3-70b-versatile\"\n", "            groq_api_key = os.getenv('GROQ_API_KEY')\n", "            groq = OpenAI(api_key=groq_api_key, base_url=\"https://api.groq.com/openai/v1\")\n", "            response = groq.chat.completions.create(model=model_name, messages=api_response)\n", "            results[model_name] = response.choices[0].message.content\n", "            print(f\"✓ Got response from {model_name}\")\n", "        except Exception as e:\n", "            print(f\"⚠️ Error with {model_name}: {str(e)}\")\n", "            # Continue with other models\n", "    \n", "    # Check if we got any responses\n", "    if not results:\n", "        print(\"⚠️ No models were able to provide a response\")\n", "    \n", "    return results\n", "\n", "# Get responses from all available models\n", "model_responses = run_prompt_on_available_models(prompt)\n", "\n", "# Display the results\n", "for model, answer in model_responses.items():\n", "    display(Markdown(f\"## Response from {model}\\n\\n{answer}\"))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<b>Sythesize answers from all models into one</b>\n", "</br>\n", "<b>- - - - - - - - - - - - - - - -</b>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a synthesis prompt\n", "synthesis_prompt = f\"\"\"\n", "Here are the responses from different models:\n", "\"\"\"\n", "\n", "# Add each model's response to the synthesis prompt without mentioning model names\n", "for index, (model, response) in enumerate(model_responses.items()):\n", "    synthesis_prompt += f\"\\n--- Response {index+1} ---\\n{response}\\n\"\n", "\n", "synthesis_prompt += \"\"\"\n", "Please synthesize these responses into one comprehensive answer that:\n", "1. Captures the best insights from each response\n", "2. Resolves any contradictions between responses\n", "3. Presents a clear and coherent final answer\n", "4. Maintains the same format as the original responses (numbered list format)\n", "5.Compiles all additional places mentioned by all models \n", "\n", "Your synthesized response:\n", "\"\"\"\n", "\n", "# Create the synthesis\n", "if check_api_key('OPENAI_API_KEY'):\n", "    try:\n", "        openai_client = OpenAI()\n", "        synthesis_response = openai_client.chat.completions.create(\n", "            model=\"gpt-4o-mini\",\n", "            messages=[{\"role\": \"user\", \"content\": synthesis_prompt}]\n", "        )\n", "        synthesized_answer = synthesis_response.choices[0].message.content\n", "        print(\"✓ Successfully synthesized responses with gpt-4o-mini\")\n", "        \n", "        # Display the synthesized answer\n", "        display(Markdown(\"## Synthesized Answer\\n\\n\" + synthesized_answer))\n", "    except Exception as e:\n", "        print(f\"⚠️ Error synthesizing responses with gpt-4o-mini: {str(e)}\")\n", "else:\n", "    print(\"⚠️ OpenAI API key not available, cannot synthesize responses\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 2}