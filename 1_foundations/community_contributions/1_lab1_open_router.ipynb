{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Welcome to the start of your adventure in Agentic AI"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../assets/stop.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#ff7800;\">Are you ready for action??</h2>\n", "            <span style=\"color:#ff7800;\">Have you completed all the setup steps in the <a href=\"../setup/\">setup</a> folder?<br/>\n", "            Have you checked out the guides in the <a href=\"../guides/01_intro.ipynb\">guides</a> folder?<br/>\n", "            Well in that case, you're ready!!\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../assets/tools.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#00bfff;\">This code is a live resource - keep an eye out for my updates</h2>\n", "            <span style=\"color:#00bfff;\">I push updates regularly. As people ask questions or have problems, I add more examples and improve explanations. As a result, the code below might not be identical to the videos, as I've added more steps and better comments. Consider this like an interactive book that accompanies the lectures.<br/><br/>\n", "            I try to send emails regularly with important updates related to the course. You can find this in the 'Announcements' section of Udemy in the left sidebar. You can also choose to receive my emails via your Notification Settings in Udemy. I'm respectful of your inbox and always try to add value with my emails!\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### And please do remember to contact me if I can help\n", "\n", "And I love to connect: https://www.linkedin.com/in/eddonner/\n", "\n", "\n", "### New to Notebooks like this one? Head over to the guides folder!\n", "\n", "Just to check you've already added the Python and Jupyter extensions to Cursor, if not already installed:\n", "- Open extensions (View >> extensions)\n", "- Search for python, and when the results show, click on the ms-python one, and Install it if not already installed\n", "- Search for jupyter, and when the results show, click on the Microsoft one, and Install it if not already installed  \n", "Then View >> Explorer to bring back the File Explorer.\n", "\n", "And then:\n", "1. Click where it says \"Select Kernel\" near the top right, and select the option called `.venv (Python 3.12.9)` or similar, which should be the first choice or the most prominent choice. You may need to choose \"Python Environments\" first.\n", "2. <PERSON>lick in each \"cell\" below, starting with the cell immediately below this text, and press Shift+Enter to run\n", "3. Enjoy!\n", "\n", "After you click \"Select Kernel\", if there is no option like `.venv (Python 3.12.9)` then please do the following:  \n", "1. On Mac: From the Cursor menu, choose Settings >> VS Code Settings (NOTE: be sure to select `VSCode Settings` not `Cursor Settings`);  \n", "On Windows PC: From the File menu, choose Preferences >> VS Code Settings(NOTE: be sure to select `VSCode Settings` not `Cursor Settings`)  \n", "2. In the Settings search bar, type \"venv\"  \n", "3. In the field \"Path to folder with a list of Virtual Environments\" put the path to the project root, like C:\\Users\\<USER>\\projects\\agents (on a Windows PC) or /Users/<USER>/projects/agents (on Mac or Linux).  \n", "And then try again.\n", "\n", "Having problems with missing Python versions in that list? Have you ever used Anaconda before? It might be interferring. Quit Cursor, bring up a new command line, and make sure that your Anaconda environment is deactivated:    \n", "`conda deactivate`  \n", "And if you still have any problems with conda and python versions, it's possible that you will need to run this too:  \n", "`conda config --set auto_activate_base false`  \n", "and then from within the Agents directory, you should be able to run `uv python list` and see the Python 3.12 version."]}, {"cell_type": "code", "execution_count": 76, "metadata": {}, "outputs": [], "source": ["# First let's do an import\n", "from dotenv import load_dotenv\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Next it's time to load the API keys into environment variables\n", "\n", "load_dotenv(override=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check the keys\n", "\n", "import os\n", "open_router_api_key = os.getenv('OPEN_ROUTER_API_KEY')\n", "\n", "if open_router_api_key:\n", "    print(f\"Open router API Key exists and begins {open_router_api_key[:8]}\")\n", "else:\n", "    print(\"Open router API Key not set - please head to the troubleshooting guide in the setup folder\")\n"]}, {"cell_type": "code", "execution_count": 79, "metadata": {}, "outputs": [], "source": ["from openai import OpenAI"]}, {"cell_type": "code", "execution_count": 80, "metadata": {}, "outputs": [], "source": ["# Initialize the client to point at OpenRouter instead of OpenAI\n", "# You can use the exact same OpenAI Python package—just swap the base_url!\n", "client = OpenAI(\n", "    base_url=\"https://openrouter.ai/api/v1\",\n", "    api_key=open_router_api_key\n", ")"]}, {"cell_type": "code", "execution_count": 81, "metadata": {}, "outputs": [], "source": ["messages = [{\"role\": \"user\", \"content\": \"What is 2+2?\"}]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["client = OpenAI(\n", "    base_url=\"https://openrouter.ai/api/v1\",\n", "    api_key=open_router_api_key\n", ")\n", "\n", "resp = client.chat.completions.create(\n", "    # Select a model from https://openrouter.ai/models and provide the model name here\n", "    model=\"meta-llama/llama-3.3-8b-instruct:free\",\n", "    messages=messages\n", ")\n", "print(resp.choices[0].message.content)"]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [], "source": ["# And now - let's ask for a question:\n", "\n", "question = \"Please propose a hard, challenging question to assess someone's IQ. Respond only with the question.\"\n", "messages = [{\"role\": \"user\", \"content\": question}]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["response = client.chat.completions.create(\n", "    model=\"meta-llama/llama-3.3-8b-instruct:free\",\n", "    messages=messages\n", ")\n", "\n", "question = response.choices[0].message.content\n", "\n", "print(question)"]}, {"cell_type": "code", "execution_count": 85, "metadata": {}, "outputs": [], "source": ["# form a new messages list\n", "\n", "messages = [{\"role\": \"user\", \"content\": question}]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ask it again\n", "\n", "response = client.chat.completions.create(\n", "    model=\"meta-llama/llama-3.3-8b-instruct:free\",\n", "    messages=messages\n", ")\n", "\n", "answer = response.choices[0].message.content\n", "print(answer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from IPython.display import Markdown, display\n", "\n", "display(<PERSON><PERSON>(answer))\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Congratulations!\n", "\n", "That was a small, simple step in the direction of Agentic AI, with your new environment!\n", "\n", "Next time things get more interesting..."]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../assets/exercise.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#ff7800;\">Exercise</h2>\n", "            <span style=\"color:#ff7800;\">Now try this commercial application:<br/>\n", "            First ask the LLM to pick a business area that might be worth exploring for an Agentic AI opportunity.<br/>\n", "            Then ask the LLM to present a pain-point in that industry - something challenging that might be ripe for an Agentic solution.<br/>\n", "            Finally have 3 third LLM call propose the Agentic AI solution.\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# First create the messages:\n", "\n", "\n", "messages = [\"Something here\"]\n", "\n", "# Then make the first call:\n", "\n", "response =\n", "\n", "# Then read the business idea:\n", "\n", "business_idea = response.\n", "\n", "# And repeat!"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}