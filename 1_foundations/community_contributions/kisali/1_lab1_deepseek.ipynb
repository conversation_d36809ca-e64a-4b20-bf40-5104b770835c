{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Submission for Week 1 Tasks"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### And please do remember to contact me if I can help\n", "\n", "And I love to connect: https://www.linkedin.com/in/ian-kisali/"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# First let's do an import. If you get an Import Error, double check that your Kernel is correct..\n", "\n", "from dotenv import load_dotenv\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Next it's time to load the API keys into environment variables\n", "# If this returns false, see the next cell!\n", "\n", "load_dotenv(override=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check the key - if you're not using DeepSeek, check whichever key you're using! <PERSON><PERSON><PERSON> doesn't need a key.\n", "\n", "import os\n", "deepseek_api_key = os.getenv('DEEPSEEK_API_KEY')\n", "\n", "if deepseek_api_key:\n", "    print(f\"DeepSeek API Key exists and begins {deepseek_api_key[:8]}\")\n", "else:\n", "    print(\"DeepSeek API Key not set - please head to the troubleshooting guide in the setup folder\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# And now - the all important import statement\n", "# If you get an import error - head over to troubleshooting in the Setup folder\n", "\n", "from openai import OpenAI"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# And now we'll create an instance of the OpenAI class\n", "# If you're not sure what it means to create an instance of a class - head over to the guides folder (guide 6)!\n", "# If you get a NameError - head over to the guides folder (guide 6)to learn about NameErrors - always instantly fixable\n", "# If you're not using DeepSeek, you just need to slightly modify this - precise instructions are in the AI APIs guide (guide 9)\n", "\n", "deepseek_client = OpenAI(api_key=deepseek_api_key, base_url=\"https://api.deepseek.com\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Models existing in DeepSeek\n", "print(deepseek_client.models.list())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a list of messages in the familiar OpenAI format\n", "\n", "messages = [{\"role\": \"user\", \"content\": \"What is 2+2?\"}]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# And now call it! Any problems, head to the troubleshooting guide\n", "# This uses deepseek-chat, the incredibly cheap model\n", "# If you get a NameError, head to the guides folder (guide 6) to learn about NameErrors - always instantly fixable\n", "\n", "response = deepseek_client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=messages\n", ")\n", "\n", "print(response.choices[0].message.content)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# And now - let's ask for a question:\n", "\n", "question = \"Please propose a hard, challenging question to assess someone's IQ. Respond only with the question.\"\n", "messages = [{\"role\": \"user\", \"content\": question}]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ask it - this uses deepseek-chat, the incredibly cheap model\n", "\n", "response = deepseek_client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=messages\n", ")\n", "\n", "question = response.choices[0].message.content\n", "\n", "print(question)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# form a new messages list\n", "messages = [{\"role\": \"user\", \"content\": question}]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ask it again\n", "response = deepseek_client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=messages\n", ")\n", "\n", "answer = response.choices[0].message.content\n", "print(answer)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from IPython.display import Markdown, display\n", "\n", "display(<PERSON><PERSON>(answer))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Task 1 Business Idea Submission\n", "\n", "That was a small, simple step in the direction of Agentic AI, with your new environment!\n", "\n", "Next time things get more interesting..."]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../../../assets/exercise.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#ff7800;\">Exercise</h2>\n", "            <span style=\"color:#ff7800;\">Now try this commercial application:<br/>\n", "            First ask the LLM to pick a business area that might be worth exploring for an Agentic AI opportunity.<br/>\n", "            Then ask the LLM to present a pain-point in that industry - something challenging that might be ripe for an Agentic solution.<br/>\n", "            Finally have 3 third LLM call propose the Agentic AI solution. <br/>\n", "            We will cover this at up-coming labs, so don't worry if you're unsure.. just give it a try!\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# First create the messages and first call for picking business ideas:\n", "question = \"Pick a business idea that might be ripe for an Agentic AI solution. The idea should be challenging and interesting and focusing on DevOps or SRE.\"\n", "messages = [{\"role\": \"user\", \"content\": question}]\n", "\n", "response = deepseek_client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=messages\n", ")\n", "business_ideas = response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# LLM call 2 to get the pain point in the business idea that might be ripe for an Agentic solution\n", "pain_point_question = f\"Present a pain-point in the {business_ideas} - something challenging that might be ripe for an Agentic solution.\"\n", "messages = [{\"role\": \"user\", \"content\": pain_point_question}]\n", "\n", "response = deepseek_client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=messages\n", ")\n", "pain_point = response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# LLM Call 3 to propose the exact Agentic AI Solution\n", "business_idea = f\"The business idea is {business_ideas} and the pain point is {pain_point}. Please propose an Agentic AI solution to the pain point. Respond only with the solution.\"\n", "messages = [{\"role\": \"user\", \"content\": business_idea}]\n", "\n", "response = deepseek_client.chat.completions.create(\n", "    model=\"deepseek-chat\",\n", "    messages=messages\n", ")\n", "\n", "agentic_ai_solution = response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(agentic_ai_solution)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["display(Markdown(agentic_ai_solution))"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}}, "nbformat": 4, "nbformat_minor": 2}