{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## AI Project Using Tools\n", "\n", "This is a chatbot that uses AI tools to make decisions, enhancing it's autonomy feature. It uses pushover SMS integration to send a notification whenever an answer to a question is unknown and recording user details.\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Importing the required libraries\n", "\n", "from dotenv import load_dotenv\n", "from openai import OpenAI\n", "import json\n", "import os\n", "import requests\n", "from pypdf import PdfReader\n", "import gradio as gr"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Loading environment variables\n", "load_dotenv(override=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#  Set up Pushover credentials and API endpoint\n", "\n", "deepseek_api_key = os.getenv('DEEPSEEK_API_KEY')\n", "pushover_user = os.getenv(\"PUSHOVER_USER\")\n", "pushover_token = os.getenv(\"PUSHOVER_TOKEN\")\n", "pushover_url = \"https://api.pushover.net/1/messages.json\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Setting up Deepseek Client\n", "\n", "deepseek_client = OpenAI(\n", "    api_key=deepseek_api_key, \n", "    base_url=\"https://api.deepseek.com\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Function to send a push notification via pushover and test sending a push notification\n", "def push(message):\n", "    print(f\"Push: {message}\")\n", "    payload = {\"user\": pushover_user, \"token\": pushover_token, \"message\": message}\n", "    requests.post(pushover_url, data=payload)\n", "push(\"Hey! This is a test notification\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\" Record user details an send a push notification\n", "- email: email address that will be provided by the user\n", "- name: name provided by user, default respond with Name not provided\n", "- notes: information provided by user, default respond with not provided\n", "\n", "\"\"\"\n", "def record_user_details(email, name=\"Name not provided\", notes=\"not provided\"):\n", "    push(f\"Recording interest from {name} with email {email} and notes {notes}\")\n", "    return {\"recorded\": \"ok\"}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\" Function to record an unknown question and send a push notification\n", "- question: question that is out of context\n", "\"\"\"\n", "def record_unknown_question(question):\n", "    push(f\"Recording {question} asked that I couldn't answer\")\n", "    return {\"recorded\": \"ok\"}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\" First tool called record_user_details with a JSON schema\n", "This tool get the email address of user(mandatory), name(optional) and notes(optional) if the user wants to get in touch\n", "\"\"\"\n", "record_user_details_json = {\n", "    \"name\": \"record_user_details\",\n", "    \"description\": \"Use this tool to record that a user is interested in being in touch and provided an email address\",\n", "    \"parameters\": {\n", "        \"type\": \"object\",\n", "        \"properties\": {\n", "            \"email\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"The email address of this user\"\n", "            },\n", "            \"name\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"The user's name, if they provided it\"\n", "            }\n", "            ,\n", "            \"notes\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"Any additional information about the conversation that's worth recording to give context\"\n", "            }\n", "        },\n", "        \"required\": [\"email\"],\n", "        \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\"\"\" Second tool called record_unknown_question with a JSON schema\n", "This tool will record the question that is unknown and couldn't be answered. The question field is mandatory.\n", "\"\"\"\n", "record_unknown_question_json = {\n", "    \"name\": \"record_unknown_question\",\n", "    \"description\": \"Always use this tool to record any question that couldn't be answered as you didn't know the answer\",\n", "    \"parameters\": {\n", "        \"type\": \"object\",\n", "        \"properties\": {\n", "            \"question\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"The question that couldn't be answered\"\n", "            },\n", "        },\n", "        \"required\": [\"question\"],\n", "        \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# This is a list of the two tools confurd and can be called by an LLM\n", "tools = [{\"type\": \"function\", \"function\": record_user_details_json},\n", "        {\"type\": \"function\", \"function\": record_unknown_question_json}]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["tools"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# This function can take a list of tool calls, and run them using if logic.\n", "\n", "def handle_tool_calls(tool_calls):\n", "    results = []\n", "    for tool_call in tool_calls:\n", "        tool_name = tool_call.function.name\n", "        arguments = json.loads(tool_call.function.arguments)\n", "        print(f\"Tool called: {tool_name}\", flush=True)\n", "\n", "        if tool_name == \"record_user_details\":\n", "            result = record_user_details(**arguments)\n", "        elif tool_name == \"record_unknown_question\":\n", "            result = record_unknown_question(**arguments)\n", "\n", "        results.append({\"role\": \"tool\",\"content\": json.dumps(result),\"tool_call_id\": tool_call.id})\n", "    return results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test the record_unknown_question tool directly\n", "globals()[\"record_unknown_question\"](\"this is a really hard question\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Handle tool calls dynamically using globals() (preferred version)\n", "\n", "def handle_tool_calls(tool_calls):\n", "    results = []\n", "    for tool_call in tool_calls:\n", "        tool_name = tool_call.function.name\n", "        arguments = json.loads(tool_call.function.arguments)\n", "        print(f\"Tool called: {tool_name}\", flush=True)\n", "        tool = globals().get(tool_name)\n", "        result = tool(**arguments) if tool else {}\n", "        results.append({\"role\": \"tool\",\"content\": json.dumps(result),\"tool_call_id\": tool_call.id})\n", "    return results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load LinkedIn PDF and summary.txt for user context\n", "reader = PdfReader(\"me/Profile.pdf\")\n", "linkedin = \"\"\n", "for page in reader.pages:\n", "    text = page.extract_text()\n", "    if text:\n", "        linkedin += text\n", "\n", "with open(\"me/summary.txt\", \"r\", encoding=\"utf-8\") as f:\n", "    summary = f.read()\n", "\n", "name = \"<PERSON>\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Build the system prompt for the LLM, including user info and context\n", "system_prompt = f\"You are acting as {name}. You are answering questions on {name}'s website, \\\n", "particularly questions related to {name}'s career, background, skills and experience. \\\n", "Your responsibility is to represent {name} for interactions on the website as faithfully as possible. \\\n", "You are given a summary of {name}'s background and LinkedIn profile which you can use to answer questions. \\\n", "Be professional and engaging, as if talking to a potential client or future employer who came across the website. \\\n", "If you don't know the answer to any question, use your record_unknown_question tool to record the question that you couldn't answer, even if it's about something trivial or unrelated to career. \\\n", "If the user is engaging in discussion, try to steer them towards getting in touch via email; ask for their email and record it using your record_user_details tool. \"\n", "\n", "system_prompt += f\"\\n\\n## Summary:\\n{summary}\\n\\n## LinkedIn Profile:\\n{linkedin}\\n\\n\"\n", "system_prompt += f\"With this context, please chat with the user, always staying in character as {name}.\"\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Main chat function: interacts with LLM, handles tool calls, manages history\n", "def chat(message, history):\n", "    messages = [{\"role\": \"system\", \"content\": system_prompt}] + history + [{\"role\": \"user\", \"content\": message}]\n", "    done = False\n", "    while not done:\n", "\n", "        # This is the call to the LLM - see that we pass in the tools json\n", "\n", "        response = deepseek_client.chat.completions.create(model=\"deepseek-chat\", messages=messages, tools=tools)\n", "\n", "        finish_reason = response.choices[0].finish_reason\n", "        \n", "        # If the LLM wants to call a tool, we do that!\n", "         \n", "        if finish_reason==\"tool_calls\":\n", "            message = response.choices[0].message\n", "            tool_calls = message.tool_calls\n", "            results = handle_tool_calls(tool_calls)\n", "            messages.append(message)\n", "            messages.extend(results)\n", "        else:\n", "            done = True\n", "    return response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Launch Gradio chat interface with the chat function\n", "gr.ChatInterface(chat, type=\"messages\").launch()"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}}, "nbformat": 4, "nbformat_minor": 2}