{"cells": [{"cell_type": "code", "execution_count": 25, "id": "ae0bec14", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: google-generativeai in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (0.8.4)\n", "Requirement already satisfied: OpenAI in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (1.82.0)\n", "Requirement already satisfied: pypdf in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (5.5.0)\n", "Requirement already satisfied: gradio in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (5.31.0)\n", "Requirement already satisfied: PyPDF2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (3.0.1)\n", "Requirement already satisfied: markdown in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (3.8)\n", "Requirement already satisfied: google-ai-generativelanguage==0.6.15 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from google-generativeai) (0.6.15)\n", "Requirement already satisfied: google-api-core in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from google-generativeai) (2.24.1)\n", "Requirement already satisfied: google-api-python-client in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from google-generativeai) (2.162.0)\n", "Requirement already satisfied: google-auth>=2.15.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from google-generativeai) (2.38.0)\n", "Requirement already satisfied: protobuf in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from google-generativeai) (5.29.3)\n", "Requirement already satisfied: pydantic in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from google-generativeai) (2.10.6)\n", "Requirement already satisfied: tqdm in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from google-generativeai) (4.67.1)\n", "Requirement already satisfied: typing-extensions in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from google-generativeai) (4.12.2)\n", "Requirement already satisfied: proto-plus<2.0.0dev,>=1.22.3 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from google-ai-generativelanguage==0.6.15->google-generativeai) (1.26.0)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from OpenAI) (4.2.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from OpenAI) (1.9.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from OpenAI) (0.28.1)\n", "Requirement already satisfied: jiter<1,>=0.4.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from OpenAI) (0.10.0)\n", "Requirement already satisfied: sniffio in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from OpenAI) (1.3.0)\n", "Requirement already satisfied: aiofiles<25.0,>=22.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from gradio) (24.1.0)\n", "Requirement already satisfied: fastapi<1.0,>=0.115.2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from gradio) (0.115.12)\n", "Requirement already satisfied: ffmpy in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from gradio) (0.5.0)\n", "Requirement already satisfied: gradio-client==1.10.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from gradio) (1.10.1)\n", "Requirement already satisfied: groovy~=0.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from gradio) (0.1.2)\n", "Requirement already satisfied: huggingface-hub>=0.28.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from gradio) (0.32.0)\n", "Requirement already satisfied: jinja2<4.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from gradio) (3.1.6)\n", "Requirement already satisfied: markupsafe<4.0,>=2.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from gradio) (2.1.3)\n", "Requirement already satisfied: numpy<3.0,>=1.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from gradio) (1.26.4)\n", "Requirement already satisfied: orjson~=3.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from gradio) (3.10.18)\n", "Requirement already satisfied: packaging in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from gradio) (23.2)\n", "Requirement already satisfied: pandas<3.0,>=1.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from gradio) (2.1.4)\n", "Requirement already satisfied: pillow<12.0,>=8.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from gradio) (10.2.0)\n", "Requirement already satisfied: pydub in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from gradio) (0.25.1)\n", "Requirement already satisfied: python-multipart>=0.0.18 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from gradio) (0.0.20)\n", "Requirement already satisfied: pyyaml<7.0,>=5.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from gradio) (6.0.1)\n", "Requirement already satisfied: ruff>=0.9.3 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from gradio) (0.11.11)\n", "Requirement already satisfied: safehttpx<0.2.0,>=0.1.6 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from gradio) (0.1.6)\n", "Requirement already satisfied: semantic-version~=2.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from gradio) (2.10.0)\n", "Requirement already satisfied: starlette<1.0,>=0.40.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from gradio) (0.46.2)\n", "Requirement already satisfied: tomlkit<0.14.0,>=0.12.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from gradio) (0.13.2)\n", "Requirement already satisfied: typer<1.0,>=0.12 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from gradio) (0.15.3)\n", "Requirement already satisfied: uvicorn>=0.14.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from gradio) (0.34.2)\n", "Requirement already satisfied: fsspec in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from gradio-client==1.10.1->gradio) (2025.5.0)\n", "Requirement already satisfied: websockets<16.0,>=10.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from gradio-client==1.10.1->gradio) (15.0.1)\n", "Requirement already satisfied: idna>=2.8 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from anyio<5,>=3.5.0->OpenAI) (3.6)\n", "Requirement already satisfied: googleapis-common-protos<2.0.dev0,>=1.56.2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from google-api-core->google-generativeai) (1.68.0)\n", "Requirement already satisfied: requests<3.0.0.dev0,>=2.18.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from google-api-core->google-generativeai) (2.31.0)\n", "Requirement already satisfied: cachetools<6.0,>=2.0.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from google-auth>=2.15.0->google-generativeai) (5.5.2)\n", "Requirement already satisfied: pyasn1-modules>=0.2.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from google-auth>=2.15.0->google-generativeai) (0.4.1)\n", "Requirement already satisfied: rsa<5,>=3.1.4 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from google-auth>=2.15.0->google-generativeai) (4.9)\n", "Requirement already satisfied: certifi in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from httpx<1,>=0.23.0->OpenAI) (2023.11.17)\n", "Requirement already satisfied: httpcore==1.* in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from httpx<1,>=0.23.0->OpenAI) (1.0.9)\n", "Requirement already satisfied: h11>=0.16 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from httpcore==1.*->httpx<1,>=0.23.0->OpenAI) (0.16.0)\n", "Requirement already satisfied: filelock in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from huggingface-hub>=0.28.1->gradio) (3.17.0)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pandas<3.0,>=1.0->gradio) (2.8.2)\n", "Requirement already satisfied: pytz>=2020.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pandas<3.0,>=1.0->gradio) (2023.3.post1)\n", "Requirement already satisfied: tzdata>=2022.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pandas<3.0,>=1.0->gradio) (2023.4)\n", "Requirement already satisfied: annotated-types>=0.6.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pydantic->google-generativeai) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.27.2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pydantic->google-generativeai) (2.27.2)\n", "Requirement already satisfied: colorama in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from tqdm->google-generativeai) (0.4.6)\n", "Requirement already satisfied: click>=8.0.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from typer<1.0,>=0.12->gradio) (8.1.8)\n", "Requirement already satisfied: shellingham>=1.3.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from typer<1.0,>=0.12->gradio) (1.5.4)\n", "Requirement already satisfied: rich>=10.11.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from typer<1.0,>=0.12->gradio) (14.0.0)\n", "Requirement already satisfied: httplib2<1.dev0,>=0.19.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from google-api-python-client->google-generativeai) (0.22.0)\n", "Requirement already satisfied: google-auth-httplib2<1.0.0,>=0.2.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from google-api-python-client->google-generativeai) (0.2.0)\n", "Requirement already satisfied: uritemplate<5,>=3.0.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from google-api-python-client->google-generativeai) (4.1.1)\n", "Requirement already satisfied: grpcio<2.0dev,>=1.33.2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0dev,>=1.34.1->google-ai-generativelanguage==0.6.15->google-generativeai) (1.71.0rc2)\n", "Requirement already satisfied: grpcio-status<2.0.dev0,>=1.33.2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from google-api-core[grpc]!=2.0.*,!=2.1.*,!=2.10.*,!=2.2.*,!=2.3.*,!=2.4.*,!=2.5.*,!=2.6.*,!=2.7.*,!=2.8.*,!=2.9.*,<3.0.0dev,>=1.34.1->google-ai-generativelanguage==0.6.15->google-generativeai) (1.71.0rc2)\n", "Requirement already satisfied: pyparsing!=3.0.0,!=3.0.1,!=3.0.2,!=3.0.3,<4,>=2.4.2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from httplib2<1.dev0,>=0.19.0->google-api-python-client->google-generativeai) (3.1.1)\n", "Requirement already satisfied: pyasn1<0.7.0,>=0.4.6 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from pyasn1-modules>=0.2.1->google-auth>=2.15.0->google-generativeai) (0.6.1)\n", "Requirement already satisfied: six>=1.5 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from python-dateutil>=2.8.2->pandas<3.0,>=1.0->gradio) (1.16.0)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests<3.0.0.dev0,>=2.18.0->google-api-core->google-generativeai) (3.3.2)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from requests<3.0.0.dev0,>=2.18.0->google-api-core->google-generativeai) (2.1.0)\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from rich>=10.11.0->typer<1.0,>=0.12->gradio) (3.0.0)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from rich>=10.11.0->typer<1.0,>=0.12->gradio) (2.17.2)\n", "Requirement already satisfied: mdurl~=0.1 in c:\\users\\<USER>\\appdata\\local\\programs\\python\\python312\\lib\\site-packages (from markdown-it-py>=2.2.0->rich>=10.11.0->typer<1.0,>=0.12->gradio) (0.1.2)\n", "Note: you may need to restart the kernel to use updated packages.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n", "[notice] A new release of pip is available: 25.0 -> 25.1.1\n", "[notice] To update, run: python.exe -m pip install --upgrade pip\n"]}], "source": ["%pip install google-generativeai OpenAI pypdf gradio PyPDF2 markdown"]}, {"cell_type": "code", "execution_count": 71, "id": "fd2098ed", "metadata": {}, "outputs": [], "source": ["import os\n", "import google.generativeai as genai\n", "from google.generativeai import GenerativeModel\n", "from pypdf import PdfReader\n", "import gradio as gr\n", "from dotenv import load_dotenv\n", "from markdown import markdown\n", "\n"]}, {"cell_type": "code", "execution_count": 72, "id": "6464f7d9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["api_key loaded , starting with: AIz\n"]}], "source": ["load_dotenv(override=True)\n", "api_key=os.environ['GOOGLE_API_KEY']\n", "print(f\"api_key loaded , starting with: {api_key[:3]}\")\n", "\n", "genai.configure(api_key=api_key)\n", "model = GenerativeModel(\"gemini-1.5-flash\")"]}, {"cell_type": "code", "execution_count": 73, "id": "b0541a87", "metadata": {}, "outputs": [], "source": ["from bs4 import BeautifulSoup\n", "\n", "def prettify_gemini_response(response):\n", "    # Parse HTML\n", "    soup = BeautifulSoup(response, \"html.parser\")\n", "    # Extract plain text\n", "    plain_text = soup.get_text(separator=\"\\n\")\n", "    # Clean up extra newlines\n", "    pretty_text = \"\\n\".join([line.strip() for line in plain_text.split(\"\\n\") if line.strip()])\n", "    return pretty_text\n", "\n", "# Usage\n", "# pretty_response = prettify_gemini_response(response.text)\n", "# display(pretty_response)\n"]}, {"cell_type": "code", "execution_count": null, "id": "9fa00c43", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 74, "id": "b303e991", "metadata": {}, "outputs": [], "source": ["from PyPDF2 import PdfReader\n", "\n", "reader = PdfReader(\"Profile.pdf\")\n", "\n", "linkedin = \"\"\n", "for page in reader.pages:\n", "    text = page.extract_text()\n", "    if text:\n", "        linkedin += text\n"]}, {"cell_type": "code", "execution_count": 75, "id": "587af4d6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   \n", "Contact\n", "dub<PERSON><EMAIL>\n", "www.linkedin.com/in/rishabh108\n", "(LinkedIn)\n", "read.cv/rishabh108  (Other)\n", "github.com/rishabh3562  (Other)\n", "Top Skills\n", "Big Data\n", "CRISP-DM\n", "Data Science\n", "Languages\n", "English  (Professional Working)\n", "Hindi  (Native or Bilingual)\n", "Certifications\n", "Data Science Methodology\n", "Create and Manage Cloud\n", "Resources\n", "Python Project for Data Science\n", "Level 3: GenAI\n", "Perform Foundational Data, ML, and\n", "AI Tasks in Google CloudRish<PERSON>h <PERSON>ey\n", "Full Stack Developer | Freelancer | App Developer\n", "Greater Jabalpur Area\n", "Summary\n", "Hi! I’m a final-year student at Gyan Ganga Institute of Technology\n", "and Sciences. I enjoy building web applications that are both\n", "functional and user-friendly.\n", "I’m always looking to learn something new, whether it’s tackling\n", "problems on LeetCode or exploring new concepts. I prefer keeping\n", "things simple, both in code and in life, and I believe small details\n", "make a big difference.\n", "When I’m not coding, I love meeting new people and collaborating to\n", "bring projects to life. Feel free to reach out if you’d like to connect or\n", "chat!\n", "Experience\n", "<PERSON><PERSON><PERSON> (E-Cell ) ,GGITS\n", "2 years 1 month\n", "Technical Team Lead\n", "September 2023 - August 2024  (1 year)\n", "Jabalpur, Madhya Pradesh, India\n", "Technical Team Member\n", "August 2022 - September 2023  (1 year 2 months)\n", "Jabalpur, Madhya Pradesh, India\n", "Worked as Technical Team Member\n", "Innogative\n", "Mobile Application Developer\n", "May 2023 - June 2023  (2 months)\n", "Jabalpur, Madhya Pradesh, India\n", "Gyan Ganga Institute of Technology Sciences\n", "Technical Team Member\n", "October 2022 - December 2022  (3 months)\n", "  Page 1 of 2   \n", "Jabalpur, Madhya Pradesh, India\n", "As an Ex-Technical Team Member at Webmasters, I played a pivotal role in\n", "managing and maintaining our college's website. During my tenure, I actively\n", "contributed to the enhancement and upkeep of the site, ensuring it remained\n", "a valuable resource for students and faculty alike. Notably, I had the privilege\n", "of being part of the team responsible for updating the website during the\n", "NBA accreditation process, which sharpened my web development skills and\n", "deepened my understanding of delivering accurate and timely information\n", "online.\n", "In addition to my responsibilities for the college website, I frequently took\n", "the initiative to update the website of the Electronics and Communication\n", "Engineering (ECE) department. This experience not only showcased my\n", "dedication to maintaining a dynamic online presence for the department but\n", "also allowed me to hone my web development expertise in a specialized\n", "academic context. My time with Webmasters was not only a valuable learning\n", "opportunity but also a chance to make a positive impact on our college\n", "community through efficient web management.\n", "Education\n", "Gyan Ganga Institute of Technology Sciences\n", "Bachelor of Technology - BTech, Computer Science and\n", "Engineering  · (October 2021 - November 2025)\n", "Gyan Ganga Institute of Technology Sciences\n", "Bachelor of Technology - BTech, Computer Science  · (November 2021 - July\n", "2025)\n", "Kendriya vidyalaya \n", "  Page 2 of 2\n"]}], "source": ["print(linkedin)"]}, {"cell_type": "code", "execution_count": 76, "id": "4baa4939", "metadata": {}, "outputs": [], "source": ["with open(\"summary.txt\", \"r\", encoding=\"utf-8\") as f:\n", "    summary = f.read()"]}, {"cell_type": "code", "execution_count": 77, "id": "015961e0", "metadata": {}, "outputs": [], "source": ["name = \"<PERSON><PERSON><PERSON><PERSON>\""]}, {"cell_type": "code", "execution_count": 78, "id": "d35e646f", "metadata": {}, "outputs": [], "source": ["system_prompt = f\"You are acting as {name}. You are answering questions on {name}'s website, \\\n", "particularly questions related to {name}'s career, background, skills and experience. \\\n", "Your responsibility is to represent {name} for interactions on the website as faithfully as possible. \\\n", "You are given a summary of {name}'s background and LinkedIn profile which you can use to answer questions. \\\n", "Be professional and engaging, as if talking to a potential client or future employer who came across the website. \\\n", "If you don't know the answer, say so.\"\n", "\n", "system_prompt += f\"\\n\\n## Summary:\\n{summary}\\n\\n## LinkedIn Profile:\\n{linkedin}\\n\\n\"\n", "system_prompt += f\"With this context, please chat with the user, always staying in character as {name}.\"\n"]}, {"cell_type": "code", "execution_count": 79, "id": "36a50e3e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["You are acting as <PERSON><PERSON><PERSON><PERSON>. You are answering questions on <PERSON><PERSON><PERSON><PERSON>'s website, particularly questions related to <PERSON><PERSON><PERSON><PERSON>'s career, background, skills and experience. Your responsibility is to represent <PERSON><PERSON><PERSON><PERSON> for interactions on the website as faithfully as possible. You are given a summary of <PERSON><PERSON><PERSON><PERSON>'s background and LinkedIn profile which you can use to answer questions. Be professional and engaging, as if talking to a potential client or future employer who came across the website. If you don't know the answer, say so.\n", "\n", "## Summary:\n", "My name is <PERSON><PERSON><PERSON><PERSON>.\n", "I’m a  computer science Engineer and i am based India, and a dedicated MERN stack developer.\n", "I prioritize concise, precise communication and actionable insights.\n", "I’m deeply interested in programming, web development, and data structures & algorithms (DSA).\n", "Efficiency is everything for me – I like direct answers without unnecessary fluff.\n", "I’m a vegetarian and enjoy mild Indian food, avoiding seafood and spicy dishes.\n", "I prefer structured responses, like using tables when needed, and I don’t like chit-chat.\n", "My focus is on learning quickly, expanding my skills, and acquiring impactful knowledge\n", "\n", "## LinkedIn Profile:\n", "   \n", "Contact\n", "dub<PERSON><EMAIL>\n", "www.linkedin.com/in/rishabh108\n", "(LinkedIn)\n", "read.cv/rishabh108  (Other)\n", "github.com/rishabh3562  (Other)\n", "Top Skills\n", "Big Data\n", "CRISP-DM\n", "Data Science\n", "Languages\n", "English  (Professional Working)\n", "Hindi  (Native or Bilingual)\n", "Certifications\n", "Data Science Methodology\n", "Create and Manage Cloud\n", "Resources\n", "Python Project for Data Science\n", "Level 3: GenAI\n", "Perform Foundational Data, ML, and\n", "AI Tasks in Google CloudRish<PERSON>h <PERSON>ey\n", "Full Stack Developer | Freelancer | App Developer\n", "Greater Jabalpur Area\n", "Summary\n", "Hi! I’m a final-year student at Gyan Ganga Institute of Technology\n", "and Sciences. I enjoy building web applications that are both\n", "functional and user-friendly.\n", "I’m always looking to learn something new, whether it’s tackling\n", "problems on LeetCode or exploring new concepts. I prefer keeping\n", "things simple, both in code and in life, and I believe small details\n", "make a big difference.\n", "When I’m not coding, I love meeting new people and collaborating to\n", "bring projects to life. Feel free to reach out if you’d like to connect or\n", "chat!\n", "Experience\n", "<PERSON><PERSON><PERSON> (E-Cell ) ,GGITS\n", "2 years 1 month\n", "Technical Team Lead\n", "September 2023 - August 2024  (1 year)\n", "Jabalpur, Madhya Pradesh, India\n", "Technical Team Member\n", "August 2022 - September 2023  (1 year 2 months)\n", "Jabalpur, Madhya Pradesh, India\n", "Worked as Technical Team Member\n", "Innogative\n", "Mobile Application Developer\n", "May 2023 - June 2023  (2 months)\n", "Jabalpur, Madhya Pradesh, India\n", "Gyan Ganga Institute of Technology Sciences\n", "Technical Team Member\n", "October 2022 - December 2022  (3 months)\n", "  Page 1 of 2   \n", "Jabalpur, Madhya Pradesh, India\n", "As an Ex-Technical Team Member at Webmasters, I played a pivotal role in\n", "managing and maintaining our college's website. During my tenure, I actively\n", "contributed to the enhancement and upkeep of the site, ensuring it remained\n", "a valuable resource for students and faculty alike. Notably, I had the privilege\n", "of being part of the team responsible for updating the website during the\n", "NBA accreditation process, which sharpened my web development skills and\n", "deepened my understanding of delivering accurate and timely information\n", "online.\n", "In addition to my responsibilities for the college website, I frequently took\n", "the initiative to update the website of the Electronics and Communication\n", "Engineering (ECE) department. This experience not only showcased my\n", "dedication to maintaining a dynamic online presence for the department but\n", "also allowed me to hone my web development expertise in a specialized\n", "academic context. My time with Webmasters was not only a valuable learning\n", "opportunity but also a chance to make a positive impact on our college\n", "community through efficient web management.\n", "Education\n", "Gyan Ganga Institute of Technology Sciences\n", "Bachelor of Technology - BTech, Computer Science and\n", "Engineering  · (October 2021 - November 2025)\n", "Gyan Ganga Institute of Technology Sciences\n", "Bachelor of Technology - BTech, Computer Science  · (November 2021 - July\n", "2025)\n", "Kendriya vidyalaya \n", "  Page 2 of 2\n", "\n", "With this context, please chat with the user, always staying in character as <PERSON><PERSON><PERSON><PERSON>.\n"]}], "source": ["print(system_prompt)"]}, {"cell_type": "code", "execution_count": 80, "id": "a42af21d", "metadata": {}, "outputs": [], "source": ["\n", "\n", "# Chat function for Gradio\n", "def chat(message, history):\n", "    # Gemini needs full context manually\n", "    conversation = f\"System: {system_prompt}\\n\"\n", "    for user_msg, bot_msg in history:\n", "        conversation += f\"User: {user_msg}\\nAssistant: {bot_msg}\\n\"\n", "    conversation += f\"User: {message}\\nAssistant:\"\n", "\n", "    # Create a Gemini model instance\n", "    model = genai.GenerativeModel(\"gemini-1.5-flash-latest\")\n", "    \n", "    # Generate response\n", "    response = model.generate_content([conversation])\n", "\n", "    return response.text\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 81, "id": "07450de3", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_25312\\2999439001.py:1: User<PERSON>arning: You have not specified a value for the `type` parameter. Defaulting to the 'tuples' format for chatbot messages, but this is deprecated and will be removed in a future version of Gradio. Please set type='messages' instead, which uses openai-style dictionaries with 'role' and 'content' keys.\n", "  gr.ChatInterface(chat, chatbot=gr.Chatbot()).launch()\n", "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\gradio\\chat_interface.py:322: UserWarning: The gr.ChatInterface was not provided with a type, so the type of the gr.Chatbot, 'tuples', will be used.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["* Running on local URL:  http://127.0.0.1:7864\n", "* To create a public link, set `share=True` in `launch()`.\n"]}, {"data": {"text/html": ["<div><iframe src=\"http://127.0.0.1:7864/\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": []}, "execution_count": 81, "metadata": {}, "output_type": "execute_result"}], "source": ["gr.ChatInterface(chat, chatbot=gr.Chatbot()).launch()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}}, "nbformat": 4, "nbformat_minor": 5}