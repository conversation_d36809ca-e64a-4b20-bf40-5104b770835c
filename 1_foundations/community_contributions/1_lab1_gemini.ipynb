{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Welcome to the start of your adventure in Agentic AI"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../../assets/stop.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#ff7800;\">Are you ready for action??</h2>\n", "            <span style=\"color:#ff7800;\">Have you completed all the setup steps in the <a href=\"../setup/\">setup</a> folder?<br/>\n", "            Have you checked out the guides in the <a href=\"../guides/01_intro.ipynb\">guides</a> folder?<br/>\n", "            Well in that case, you're ready!!\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../../assets/tools.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#00bfff;\">Treat these labs as a resource</h2>\n", "            <span style=\"color:#00bfff;\">I push updates to the code regularly. When people ask questions or have problems, I incorporate it in the code, adding more examples or improved commentary. As a result, you'll notice that the code below isn't identical to the videos. Everything from the videos is here; but in addition, I've added more steps and better explanations. Consider this like an interactive book that accompanies the lectures.\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### And please do remember to contact me if I can help\n", "\n", "And I love to connect: https://www.linkedin.com/in/eddonner/\n", "\n", "\n", "### New to Notebooks like this one? Head over to the guides folder!\n", "\n", "Just to check you've already added the Python and Jupyter extensions to Cursor, if not already installed:\n", "- Open extensions (View >> extensions)\n", "- Search for python, and when the results show, click on the ms-python one, and Install it if not already installed\n", "- Search for jupyter, and when the results show, click on the Microsoft one, and Install it if not already installed  \n", "Then View >> Explorer to bring back the File Explorer.\n", "\n", "And then:\n", "1. Run `uv add google-genai` to install the Google Gemini library. (If you had started your environment before running this command, you will need to restart your environment in the Jupyter notebook.)\n", "2. Click where it says \"Select Kernel\" near the top right, and select the option called `.venv (Python 3.12.9)` or similar, which should be the first choice or the most prominent choice. You may need to choose \"Python Environments\" first.\n", "3. <PERSON>lick in each \"cell\" below, starting with the cell immediately below this text, and press Shift+Enter to run\n", "4. Enjoy!\n", "\n", "After you click \"Select Kernel\", if there is no option like `.venv (Python 3.12.9)` then please do the following:  \n", "1. From the Cursor menu, choose Settings >> VSCode Settings (NOTE: be sure to select `VSCode Settings` not `Cursor Settings`)  \n", "2. In the Settings search bar, type \"venv\"  \n", "3. In the field \"Path to folder with a list of Virtual Environments\" put the path to the project root, like C:\\Users\\<USER>\\projects\\agents (on a Windows PC) or /Users/<USER>/projects/agents (on Mac or Linux).  \n", "And then try again.\n", "\n", "Having problems with missing Python versions in that list? Have you ever used Anaconda before? It might be interferring. Quit Cursor, bring up a new command line, and make sure that your Anaconda environment is deactivated:    \n", "`conda deactivate`  \n", "And if you still have any problems with conda and python versions, it's possible that you will need to run this too:  \n", "`conda config --set auto_activate_base false`  \n", "and then from within the Agents directory, you should be able to run `uv python list` and see the Python 3.12 version."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# First let's do an import\n", "from dotenv import load_dotenv\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Next it's time to load the API keys into environment variables\n", "\n", "load_dotenv(override=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check the keys\n", "\n", "import os\n", "gemini_api_key = os.getenv('GEMINI_API_KEY')\n", "\n", "if gemini_api_key:\n", "    print(f\"Gemini API Key exists and begins {gemini_api_key[:8]}\")\n", "else:\n", "    print(\"Gemini API Key not set - please head to the troubleshooting guide in the guides folder\")\n", "    \n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# And now - the all important import statement\n", "# If you get an import error - head over to troubleshooting guide\n", "\n", "from google import genai"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# And now we'll create an instance of the Gemini GenAI class\n", "# If you're not sure what it means to create an instance of a class - head over to the guides folder!\n", "# If you get a NameError - head over to the guides folder to learn about NameErrors\n", "\n", "client = genai.Client(api_key=gemini_api_key)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a list of messages in the familiar Gemini GenAI format\n", "\n", "messages = [\"What is 2+2?\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# And now call it! Any problems, head to the troubleshooting guide\n", "\n", "response = client.models.generate_content(\n", "    model=\"gemini-2.0-flash\", contents=messages\n", ")\n", "\n", "print(response.text)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# Lets no create a challenging question\n", "question = \"Please propose a hard, challenging question to assess someone's IQ. Respond only with the question.\"\n", "\n", "# Ask the the model\n", "response = client.models.generate_content(\n", "    model=\"gemini-2.0-flash\", contents=question\n", ")\n", "\n", "question = response.text\n", "\n", "print(question)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ask the models generated question to the model\n", "response = client.models.generate_content(\n", "    model=\"gemini-2.0-flash\", contents=question\n", ")\n", "\n", "# Extract the answer from the response\n", "answer = response.text\n", "\n", "# Debug log the answer\n", "print(answer)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from IPython.display import Markdown, display\n", "\n", "# Nicely format the answer using Markdown\n", "display(<PERSON><PERSON>(answer))\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Congratulations!\n", "\n", "That was a small, simple step in the direction of Agentic AI, with your new environment!\n", "\n", "Next time things get more interesting..."]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../assets/exercise.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#ff7800;\">Exercise</h2>\n", "            <span style=\"color:#ff7800;\">Now try this commercial application:<br/>\n", "            First ask the LLM to pick a business area that might be worth exploring for an Agentic AI opportunity.<br/>\n", "            Then ask the LLM to present a pain-point in that industry - something challenging that might be ripe for an Agentic solution.<br/>\n", "            Finally have 3 third LLM call propose the Agentic AI solution.\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# First create the messages:\n", "\n", "\n", "messages = [\"Something here\"]\n", "\n", "# Then make the first call:\n", "\n", "response =\n", "\n", "# Then read the business idea:\n", "\n", "business_idea = response.\n", "\n", "# And repeat!"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 2}