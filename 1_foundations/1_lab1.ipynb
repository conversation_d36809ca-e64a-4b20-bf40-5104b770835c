{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Welcome to the start of your adventure in Agentic AI"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../assets/stop.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#ff7800;\">Are you ready for action??</h2>\n", "            <span style=\"color:#ff7800;\">Have you completed all the setup steps in the <a href=\"../setup/\">setup</a> folder?<br/>\n", "            Have you read the <a href=\"../README.md\">README</a>? Many common questions are answered here!<br/>\n", "            Have you checked out the guides in the <a href=\"../guides/01_intro.ipynb\">guides</a> folder?<br/>\n", "            Well in that case, you're ready!!\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../assets/tools.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#00bfff;\">This code is a live resource - keep an eye out for my updates</h2>\n", "            <span style=\"color:#00bfff;\">I push updates regularly. As people ask questions or have problems, I add more examples and improve explanations. As a result, the code below might not be identical to the videos, as I've added more steps and better comments. Consider this like an interactive book that accompanies the lectures.<br/><br/>\n", "            I try to send emails regularly with important updates related to the course. You can find this in the 'Announcements' section of Udemy in the left sidebar. You can also choose to receive my emails via your Notification Settings in Udemy. I'm respectful of your inbox and always try to add value with my emails!\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### And please do remember to contact me if I can help\n", "\n", "And I love to connect: https://www.linkedin.com/in/eddonner/\n", "\n", "\n", "### New to Notebooks like this one? Head over to the guides folder!\n", "\n", "Just to check you've already added the Python and Jupyter extensions to Cursor, if not already installed:\n", "- Open extensions (View >> extensions)\n", "- Search for python, and when the results show, click on the ms-python one, and Install it if not already installed\n", "- Search for jupyter, and when the results show, click on the Microsoft one, and Install it if not already installed  \n", "Then View >> Explorer to bring back the File Explorer.\n", "\n", "And then:\n", "1. Click where it says \"Select Kernel\" near the top right, and select the option called `.venv (Python 3.12.9)` or similar, which should be the first choice or the most prominent choice. You may need to choose \"Python Environments\" first.\n", "2. <PERSON>lick in each \"cell\" below, starting with the cell immediately below this text, and press Shift+Enter to run\n", "3. Enjoy!\n", "\n", "After you click \"Select Kernel\", if there is no option like `.venv (Python 3.12.9)` then please do the following:  \n", "1. On Mac: From the Cursor menu, choose Settings >> VS Code Settings (NOTE: be sure to select `VSCode Settings` not `Cursor Settings`);  \n", "On Windows PC: From the File menu, choose Preferences >> VS Code Settings(NOTE: be sure to select `VSCode Settings` not `Cursor Settings`)  \n", "2. In the Settings search bar, type \"venv\"  \n", "3. In the field \"Path to folder with a list of Virtual Environments\" put the path to the project root, like C:\\Users\\<USER>\\projects\\agents (on a Windows PC) or /Users/<USER>/projects/agents (on Mac or Linux).  \n", "And then try again.\n", "\n", "Having problems with missing Python versions in that list? Have you ever used Anaconda before? It might be interferring. Quit Cursor, bring up a new command line, and make sure that your Anaconda environment is deactivated:    \n", "`conda deactivate`  \n", "And if you still have any problems with conda and python versions, it's possible that you will need to run this too:  \n", "`conda config --set auto_activate_base false`  \n", "and then from within the Agents directory, you should be able to run `uv python list` and see the Python 3.12 version."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# First let's do an import. If you get an Import Error, double check that your Kernel is correct..\n", "\n", "from dotenv import load_dotenv\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Next it's time to load the API keys into environment variables\n", "# If this returns false, see the next cell!\n", "\n", "load_dotenv(override=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Wait, did that just output `False`??\n", "\n", "If so, the most common reason is that you didn't save your `.env` file after adding the key! Be sure to have saved.\n", "\n", "Also, make sure the `.env` file is named precisely `.env` and is in the project root directory (`agents`)\n", "\n", "By the way, your `.env` file should have a stop symbol next to it in Cursor on the left, and that's actually a good thing: that's <PERSON><PERSON><PERSON> saying to you, \"hey, I realize this is a file filled with secret information, and I'm not going to send it to an external AI to suggest changes, because your keys should not be shown to anyone else.\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../assets/stop.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#ff7800;\">Final reminders</h2>\n", "            <span style=\"color:#ff7800;\">1. If you're not confident about Environment Variables or Web Endpoints / APIs, please read Topics 3 and 5 in this <a href=\"../guides/04_technical_foundations.ipynb\">technical foundations guide</a>.<br/>\n", "            2. If you want to use AIs other than OpenAI, like Gemini, DeepSeek or Ollama (free), please see the first section in this <a href=\"../guides/09_ai_apis_and_ollama.ipynb\">AI APIs guide</a>.<br/>\n", "            3. If you ever get a Name Error in Python, you can always fix it immediately; see the last section of this <a href=\"../guides/06_python_foundations.ipynb\">Python Foundations guide</a> and follow both tutorials and exercises.<br/>\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Check the key - if you're not using OpenAI, check whichever key you're using! <PERSON><PERSON><PERSON> doesn't need a key.\n", "\n", "import os\n", "openai_api_key = os.getenv('OPENAI_API_KEY')\n", "\n", "if openai_api_key:\n", "    print(f\"OpenAI API Key exists and begins {openai_api_key[:8]}\")\n", "else:\n", "    print(\"OpenAI API Key not set - please head to the troubleshooting guide in the setup folder\")\n", "    \n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# And now - the all important import statement\n", "# If you get an import error - head over to troubleshooting in the Setup folder\n", "# Even for other LLM providers like Gemini, you still use this OpenAI import - see Guide 9 for why\n", "\n", "from openai import OpenAI"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# And now we'll create an instance of the OpenAI class\n", "# If you're not sure what it means to create an instance of a class - head over to the guides folder (guide 6)!\n", "# If you get a NameError - head over to the guides folder (guide 6)to learn about NameErrors - always instantly fixable\n", "# If you're not using OpenAI, you just need to slightly modify this - precise instructions are in the AI APIs guide (guide 9)\n", "\n", "openai = OpenAI()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a list of messages in the familiar OpenAI format\n", "\n", "messages = [{\"role\": \"user\", \"content\": \"What is 2+2?\"}]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# And now call it! Any problems, head to the troubleshooting guide\n", "# This uses GPT 4.1 nano, the incredibly cheap model\n", "# The APIs guide (guide 9) has exact instructions for using even cheaper or free alternatives to OpenAI\n", "# If you get a NameError, head to the guides folder (guide 6) to learn about NameErrors - always instantly fixable\n", "\n", "response = openai.chat.completions.create(\n", "    model=\"gpt-4.1-nano\",\n", "    messages=messages\n", ")\n", "\n", "print(response.choices[0].message.content)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# And now - let's ask for a question:\n", "\n", "question = \"Please propose a hard, challenging question to assess someone's IQ. Respond only with the question.\"\n", "messages = [{\"role\": \"user\", \"content\": question}]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ask it - this uses GPT 4.1 mini, still cheap but more powerful than nano\n", "\n", "response = openai.chat.completions.create(\n", "    model=\"gpt-4.1-mini\",\n", "    messages=messages\n", ")\n", "\n", "question = response.choices[0].message.content\n", "\n", "print(question)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# form a new messages list\n", "messages = [{\"role\": \"user\", \"content\": question}]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Ask it again\n", "\n", "response = openai.chat.completions.create(\n", "    model=\"gpt-4.1-mini\",\n", "    messages=messages\n", ")\n", "\n", "answer = response.choices[0].message.content\n", "print(answer)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from IPython.display import Markdown, display\n", "\n", "display(<PERSON><PERSON>(answer))\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Congratulations!\n", "\n", "That was a small, simple step in the direction of Agentic AI, with your new environment!\n", "\n", "Next time things get more interesting..."]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../assets/exercise.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#ff7800;\">Exercise</h2>\n", "            <span style=\"color:#ff7800;\">Now try this commercial application:<br/>\n", "            First ask the LLM to pick a business area that might be worth exploring for an Agentic AI opportunity.<br/>\n", "            Then ask the LLM to present a pain-point in that industry - something challenging that might be ripe for an Agentic solution.<br/>\n", "            Finally have 3 third LLM call propose the Agentic AI solution. <br/>\n", "            We will cover this at up-coming labs, so don't worry if you're unsure.. just give it a try!\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# First create the messages:\n", "\n", "messages = [{\"role\": \"user\", \"content\": \"Something here\"}]\n", "\n", "# Then make the first call:\n", "\n", "response =\n", "\n", "# Then read the business idea:\n", "\n", "business_idea = response.\n", "\n", "# And repeat! In the next message, include the business idea within the message"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}