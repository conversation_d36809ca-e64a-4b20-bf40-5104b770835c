# This file was autogenerated by uv via the following command:
#    uv pip compile pyproject.toml -o requirements.txt
aiofiles==24.1.0
    # via
    #   autogen-ext
    #   gradio
aiohappyeyeballs==2.6.1
    # via aiohttp
aiohttp==3.12.12
    # via
    #   langchain-community
    #   semantic-kernel
aioice==0.10.1
    # via aiortc
aiortc==1.13.0
    # via semantic-kernel
aiosignal==1.3.2
    # via aiohttp
aiosqlite==0.21.0
    # via langgraph-checkpoint-sqlite
annotated-types==0.7.0
    # via pydantic
anthropic==0.53.0
    # via
    #   agents (pyproject.toml)
    #   langchain-anthropic
anyio==4.9.0
    # via
    #   anthropic
    #   gradio
    #   httpx
    #   mcp
    #   openai
    #   sse-starlette
    #   starlette
asttokens==3.0.0
    # via stack-data
attrs==25.3.0
    # via
    #   aiohttp
    #   jsonschema
    #   referencing
autogen-agentchat==0.6.1
    # via agents (pyproject.toml)
autogen-core==0.6.1
    # via
    #   autogen-agentchat
    #   autogen-ext
autogen-ext==0.6.1
    # via agents (pyproject.toml)
av==14.4.0
    # via aiortc
azure-ai-agents==1.0.1
    # via
    #   azure-ai-projects
    #   semantic-kernel
azure-ai-projects==1.0.0b11
    # via semantic-kernel
azure-core==1.34.0
    # via
    #   azure-ai-agents
    #   azure-ai-projects
    #   azure-identity
    #   azure-storage-blob
azure-identity==1.23.0
    # via semantic-kernel
azure-storage-blob==12.25.1
    # via azure-ai-projects
beautifulsoup4==4.13.4
    # via
    #   bs4
    #   markdownify
    #   readabilipy
    #   wikipedia
bs4==0.0.2
    # via agents (pyproject.toml)
certifi==2025.4.26
    # via
    #   httpcore
    #   httpx
    #   polygon-api-client
    #   requests
cffi==1.17.1
    # via
    #   aiortc
    #   cryptography
    #   pylibsrtp
chardet==5.2.0
    # via prance
charset-normalizer==3.4.2
    # via requests
click==8.2.1
    # via
    #   typer
    #   uvicorn
cloudevents==1.12.0
    # via semantic-kernel
colorama==0.4.6
    # via griffe
comm==0.2.2
    # via ipywidgets
cryptography==45.0.4
    # via
    #   aiortc
    #   azure-identity
    #   azure-storage-blob
    #   msal
    #   pyjwt
    #   pyopenssl
dataclasses-json==0.6.7
    # via langchain-community
decorator==5.2.1
    # via ipython
defusedxml==0.7.1
    # via semantic-kernel
deprecation==2.1.0
    # via cloudevents
distro==1.9.0
    # via
    #   anthropic
    #   openai
dnspython==2.7.0
    # via aioice
ecdsa==0.19.1
    # via sendgrid
executing==2.2.0
    # via stack-data
fastapi==0.115.12
    # via gradio
ffmpy==0.6.0
    # via gradio
filelock==3.18.0
    # via huggingface-hub
frozenlist==1.7.0
    # via
    #   aiohttp
    #   aiosignal
fsspec==2025.5.1
    # via
    #   gradio-client
    #   huggingface-hub
google-crc32c==1.7.1
    # via aiortc
gradio==5.33.1
    # via agents (pyproject.toml)
gradio-client==1.10.3
    # via gradio
greenlet==3.2.3
    # via playwright
griffe==1.7.3
    # via openai-agents
groovy==0.1.2
    # via gradio
grpcio==1.70.0
    # via autogen-ext
h11==0.16.0
    # via
    #   httpcore
    #   uvicorn
hf-xet==1.1.3
    # via huggingface-hub
html5lib==1.1
    # via readabilipy
httpcore==1.0.9
    # via httpx
httpx==0.28.1
    # via
    #   agents (pyproject.toml)
    #   anthropic
    #   gradio
    #   gradio-client
    #   langgraph-sdk
    #   langsmith
    #   mcp
    #   ollama
    #   openai
    #   safehttpx
httpx-sse==0.4.0
    # via
    #   langchain-community
    #   mcp
huggingface-hub==0.32.4
    # via
    #   gradio
    #   gradio-client
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
    #   yarl
ifaddr==0.2.0
    # via aioice
importlib-metadata==8.7.0
    # via opentelemetry-api
ipython==9.3.0
    # via ipywidgets
ipython-pygments-lexers==1.1.1
    # via ipython
ipywidgets==8.1.7
    # via agents (pyproject.toml)
isodate==0.7.2
    # via
    #   azure-ai-agents
    #   azure-ai-projects
    #   azure-storage-blob
    #   openapi-core
jedi==0.19.2
    # via ipython
jinja2==3.1.6
    # via
    #   gradio
    #   semantic-kernel
jiter==0.10.0
    # via
    #   anthropic
    #   openai
jsonpatch==1.33
    # via langchain-core
jsonpointer==3.0.0
    # via jsonpatch
jsonref==1.1.0
    # via autogen-core
jsonschema==4.24.0
    # via
    #   openapi-core
    #   openapi-schema-validator
    #   openapi-spec-validator
jsonschema-path==0.3.4
    # via
    #   openapi-core
    #   openapi-spec-validator
jsonschema-specifications==2025.4.1
    # via
    #   jsonschema
    #   openapi-schema-validator
jupyterlab-widgets==3.0.15
    # via ipywidgets
langchain==0.3.25
    # via langchain-community
langchain-anthropic==0.3.15
    # via agents (pyproject.toml)
langchain-community==0.3.24
    # via
    #   agents (pyproject.toml)
    #   langchain-experimental
langchain-core==0.3.64
    # via
    #   langchain
    #   langchain-anthropic
    #   langchain-community
    #   langchain-experimental
    #   langchain-openai
    #   langchain-text-splitters
    #   langgraph
    #   langgraph-checkpoint
    #   langgraph-prebuilt
langchain-experimental==0.3.4
    # via agents (pyproject.toml)
langchain-openai==0.3.21
    # via agents (pyproject.toml)
langchain-text-splitters==0.3.8
    # via langchain
langgraph==0.4.8
    # via agents (pyproject.toml)
langgraph-checkpoint==2.0.26
    # via
    #   langgraph
    #   langgraph-checkpoint-sqlite
    #   langgraph-prebuilt
langgraph-checkpoint-sqlite==2.0.10
    # via agents (pyproject.toml)
langgraph-prebuilt==0.2.2
    # via langgraph
langgraph-sdk==0.1.70
    # via langgraph
langsmith==0.3.45
    # via
    #   agents (pyproject.toml)
    #   langchain
    #   langchain-community
    #   langchain-core
lazy-object-proxy==1.11.0
    # via openapi-spec-validator
lxml==5.4.0
    # via
    #   agents (pyproject.toml)
    #   readabilipy
markdown-it-py==3.0.0
    # via rich
markdownify==1.1.0
    # via mcp-server-fetch
markupsafe==3.0.2
    # via
    #   gradio
    #   jinja2
    #   werkzeug
marshmallow==3.26.1
    # via dataclasses-json
matplotlib-inline==0.1.7
    # via ipython
mcp==1.9.3
    # via
    #   agents (pyproject.toml)
    #   autogen-ext
    #   mcp-server-fetch
    #   openai-agents
mcp-server-fetch==2025.1.17
    # via agents (pyproject.toml)
mdurl==0.1.2
    # via markdown-it-py
more-itertools==10.7.0
    # via openapi-core
msal==1.32.3
    # via
    #   azure-identity
    #   msal-extensions
msal-extensions==1.3.1
    # via azure-identity
multidict==6.4.4
    # via
    #   aiohttp
    #   yarl
mypy-extensions==1.1.0
    # via typing-inspect
narwhals==1.42.0
    # via plotly
nest-asyncio==1.6.0
    # via semantic-kernel
numpy==2.3.0
    # via
    #   gradio
    #   langchain-community
    #   pandas
    #   scipy
    #   semantic-kernel
ollama==0.5.1
    # via autogen-ext
openai==1.85.0
    # via
    #   agents (pyproject.toml)
    #   autogen-ext
    #   langchain-openai
    #   openai-agents
    #   semantic-kernel
openai-agents==0.0.17
    # via agents (pyproject.toml)
openapi-core==0.19.5
    # via semantic-kernel
openapi-schema-validator==0.6.3
    # via
    #   openapi-core
    #   openapi-spec-validator
openapi-spec-validator==0.7.2
    # via openapi-core
opentelemetry-api==1.34.1
    # via
    #   autogen-core
    #   opentelemetry-sdk
    #   opentelemetry-semantic-conventions
    #   semantic-kernel
opentelemetry-sdk==1.34.1
    # via semantic-kernel
opentelemetry-semantic-conventions==0.55b1
    # via opentelemetry-sdk
orjson==3.10.18
    # via
    #   gradio
    #   langgraph-sdk
    #   langsmith
ormsgpack==1.10.0
    # via langgraph-checkpoint
packaging==24.2
    # via
    #   deprecation
    #   gradio
    #   gradio-client
    #   huggingface-hub
    #   langchain-core
    #   langsmith
    #   marshmallow
    #   plotly
    #   prance
pandas==2.3.0
    # via gradio
parse==1.20.2
    # via openapi-core
parso==0.8.4
    # via jedi
pathable==0.4.4
    # via jsonschema-path
pexpect==4.9.0
    # via ipython
pillow==11.2.1
    # via
    #   autogen-core
    #   gradio
playwright==1.52.0
    # via agents (pyproject.toml)
plotly==6.1.2
    # via agents (pyproject.toml)
polygon-api-client==1.14.6
    # via agents (pyproject.toml)
prance==********
    # via semantic-kernel
prompt-toolkit==3.0.51
    # via ipython
propcache==0.3.2
    # via
    #   aiohttp
    #   yarl
protego==0.4.0
    # via mcp-server-fetch
protobuf==5.29.5
    # via
    #   autogen-core
    #   semantic-kernel
psutil==7.0.0
    # via agents (pyproject.toml)
ptyprocess==0.7.0
    # via pexpect
pure-eval==0.2.3
    # via stack-data
pybars4==0.9.13
    # via semantic-kernel
pycparser==2.22
    # via cffi
pydantic==2.11.5
    # via
    #   anthropic
    #   autogen-core
    #   fastapi
    #   gradio
    #   langchain
    #   langchain-anthropic
    #   langchain-core
    #   langgraph
    #   langsmith
    #   mcp
    #   mcp-server-fetch
    #   ollama
    #   openai
    #   openai-agents
    #   pydantic-settings
    #   semantic-kernel
pydantic-core==2.33.2
    # via pydantic
pydantic-settings==2.9.1
    # via
    #   langchain-community
    #   mcp
    #   semantic-kernel
pydub==0.25.1
    # via gradio
pyee==13.0.0
    # via
    #   aiortc
    #   playwright
pygments==2.19.1
    # via
    #   ipython
    #   ipython-pygments-lexers
    #   rich
pyjwt==2.10.1
    # via msal
pylibsrtp==0.12.0
    # via aiortc
pymeta3==0.5.1
    # via pybars4
pyopenssl==25.1.0
    # via aiortc
pypdf==5.6.0
    # via agents (pyproject.toml)
pypdf2==3.0.1
    # via agents (pyproject.toml)
python-dateutil==2.9.0.post0
    # via pandas
python-dotenv==1.1.0
    # via
    #   agents (pyproject.toml)
    #   mcp
    #   pydantic-settings
python-http-client==3.3.7
    # via sendgrid
python-multipart==0.0.20
    # via
    #   gradio
    #   mcp
pytz==2025.2
    # via pandas
pyyaml==6.0.2
    # via
    #   gradio
    #   huggingface-hub
    #   jsonschema-path
    #   langchain
    #   langchain-community
    #   langchain-core
readabilipy==0.3.0
    # via mcp-server-fetch
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-path
    #   jsonschema-specifications
regex==2024.11.6
    # via
    #   readabilipy
    #   tiktoken
requests==2.32.4
    # via
    #   agents (pyproject.toml)
    #   azure-core
    #   huggingface-hub
    #   jsonschema-path
    #   langchain
    #   langchain-community
    #   langsmith
    #   mcp-server-fetch
    #   msal
    #   openai-agents
    #   prance
    #   requests-toolbelt
    #   tiktoken
    #   wikipedia
requests-toolbelt==1.0.0
    # via langsmith
rfc3339-validator==0.1.4
    # via openapi-schema-validator
rich==14.0.0
    # via typer
rpds-py==0.25.1
    # via
    #   jsonschema
    #   referencing
ruamel-yaml==0.18.14
    # via prance
ruamel-yaml-clib==0.2.12
    # via ruamel-yaml
ruff==0.11.13
    # via gradio
safehttpx==0.1.6
    # via gradio
scipy==1.15.3
    # via semantic-kernel
semantic-kernel==1.32.2
    # via agents (pyproject.toml)
semantic-version==2.10.0
    # via gradio
sendgrid==6.12.3
    # via agents (pyproject.toml)
setuptools==80.9.0
    # via agents (pyproject.toml)
shellingham==1.5.4
    # via typer
six==1.17.0
    # via
    #   azure-core
    #   ecdsa
    #   html5lib
    #   markdownify
    #   python-dateutil
    #   rfc3339-validator
smithery==0.1.0
    # via agents (pyproject.toml)
sniffio==1.3.1
    # via
    #   anthropic
    #   anyio
    #   openai
soupsieve==2.7
    # via beautifulsoup4
speedtest-cli==2.1.3
    # via agents (pyproject.toml)
sqlalchemy==2.0.41
    # via
    #   langchain
    #   langchain-community
sqlite-vec==0.1.6
    # via langgraph-checkpoint-sqlite
sse-starlette==2.3.6
    # via mcp
stack-data==0.6.3
    # via ipython
starlette==0.46.2
    # via
    #   fastapi
    #   gradio
    #   mcp
tenacity==9.1.2
    # via
    #   langchain-community
    #   langchain-core
tiktoken==0.9.0
    # via
    #   autogen-ext
    #   langchain-openai
tomlkit==0.13.3
    # via gradio
tqdm==4.67.1
    # via
    #   huggingface-hub
    #   openai
traitlets==5.14.3
    # via
    #   comm
    #   ipython
    #   ipywidgets
    #   matplotlib-inline
typer==0.16.0
    # via
    #   gradio
    #   mcp
types-requests==2.32.0.20250602
    # via openai-agents
typing-extensions==4.14.0
    # via
    #   aiosqlite
    #   anthropic
    #   anyio
    #   autogen-core
    #   azure-ai-agents
    #   azure-ai-projects
    #   azure-core
    #   azure-identity
    #   azure-storage-blob
    #   beautifulsoup4
    #   fastapi
    #   gradio
    #   gradio-client
    #   huggingface-hub
    #   langchain-core
    #   openai
    #   openai-agents
    #   openapi-core
    #   opentelemetry-api
    #   opentelemetry-sdk
    #   opentelemetry-semantic-conventions
    #   pydantic
    #   pydantic-core
    #   pyee
    #   pyopenssl
    #   referencing
    #   semantic-kernel
    #   sqlalchemy
    #   typer
    #   typing-inspect
    #   typing-inspection
typing-inspect==0.9.0
    # via dataclasses-json
typing-inspection==0.4.1
    # via
    #   pydantic
    #   pydantic-settings
tzdata==2025.2
    # via pandas
urllib3==2.4.0
    # via
    #   polygon-api-client
    #   requests
    #   types-requests
uvicorn==0.34.3
    # via
    #   gradio
    #   mcp
wcwidth==0.2.13
    # via prompt-toolkit
webencodings==0.5.1
    # via html5lib
websockets==14.2
    # via
    #   gradio-client
    #   polygon-api-client
    #   semantic-kernel
werkzeug==3.1.1
    # via
    #   openapi-core
    #   sendgrid
widgetsnbextension==4.0.14
    # via ipywidgets
wikipedia==1.4.0
    # via agents (pyproject.toml)
xxhash==3.5.0
    # via langgraph
yarl==1.20.1
    # via aiohttp
zipp==3.23.0
    # via importlib-metadata
zstandard==0.23.0
    # via langsmith
