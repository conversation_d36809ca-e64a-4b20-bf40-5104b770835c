{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# <PERSON><PERSON> and <PERSON><PERSON><PERSON>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This guide is all about using source code control: Git and Github.\n", "\n", "By the end of this, you should be confident with every day code control processes, including fetching the latest code and submitting a PR to merge your own changes."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Foundational briefing\n", "\n", "Here is <PERSON><PERSON> and <PERSON><PERSON><PERSON> for a PC or Mac audience:\n", "\n", "https://chatgpt.com/share/68061486-08b8-8012-97bc-3264ad5ebcd4"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Pulling latest code\n", "\n", "I regularly add improvements to the course with new examples, exercises and materials.\n", "\n", "Here are instructions for how to bring in the latest - the easy way, and the rigorous way!\n", "\n", "https://chatgpt.com/share/6806178b-0700-8012-836f-7e87b2670b7b"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Contributing your changes to the repo to share your contributions with others\n", "\n", "Here are step by step instructions for submitting a PR.\n", "\n", "I'd be so grateful to include your contributions. It adds value for all other students, and I love to see it myself! As an added benefit, you get recognition in Github as a contributor to the repo.\n", "\n", "Here are detailed instructions and explanations:\n", "\n", "https://chatgpt.com/share/6873c22b-2a1c-8012-bc9a-debdcf7c835b\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "plaintext"}}, "source": ["### If you'd like to become a Git pro\n", "\n", "If you want to go deep on using <PERSON><PERSON>, here is a brilliant guide. Read this and you will know much more than me!\n", "\n", "https://beej.us/guide/bggit/\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}