{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## How to start working on your idea\n", "\n", "So you've had an idea for something you'd like to build.\n", "\n", "CONGRATULATIONS! That's the most important part already done. The idea is everything!\n", "\n", "But now you're unsure where to begin. I get a lot of questions about this.\n", "\n", "I want to give you three essential pieces of advice.\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Advice 1: Be a Scientist\n", "\n", "Being an AI Engineer involves wearing 2 hats: being a Software Engineer, and a Data Scientist.\n", "\n", "At the start of your project, it's crucial to take off your Software Engineering hat and firmly put on your Data Science hat.\n", "\n", "A lot of people from a software engineering background (myself included!) struggle with this. It's common for people to fall into their comfort zone, asking questions about architecture, design, data pipes, scalability, deployment.\n", "\n", "These are all great questions, but they are not the critical ones at this early stage. They are not the hard questions. They won't make or break your idea.\n", "\n", "For the critical questions, you need to be a Scientist.\n", "\n", "Ask yourself:  \n", "1. How will you evaluate success of your model? What will you measure?  \n", "2. What data do you have, and what data do you need?  \n", "3. How can you build small prototypes to test different techniques and understand model performance?\n", "\n", "Address the science questions first - what you can achieve with LLMs and how.\n", "\n", "And I recommend starting in a Notebook before you work in python modules, to force yourself to operate with this scientific mindset.\n", "\n", "This is even more crucial with Agent projects. It can be tempting to draw up a big Agent architecture diagram on how your agents will collaborate, like a technical diagram. But this is thinking like a Software Engineer. I recommend approaching it differently; experiment with different approaches, investigate what works well, test hypotheses and iterate."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Advice 2: Embrace R&D\n", "\n", "OK so this is pretty similar to Advice 1, but worth repeating!\n", "\n", "I often get asked questions like: \"Ed, I want to build the following solution. Should I use model A, B, or C? Should I use Agents, RAG or fine-tuning?\"\n", "\n", "My answer is almost always:  \n", "- You should do all of the above! The key is to experiment; (a) come up with an evaluation criteria, (b) develop a curated dataset, (c) test different ideas and see how they perform\n", "- I often have an instinct for which will work best, but my instinct is often wrong! Don't trust me: try it yourself  \n", "- There's simply no substitute for experimentation."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Advice 3: Dream big but start SMALL!\n", "\n", "I also often have students send me 200-300 lines of code saying, \"<PERSON>, this isn't performing, please fix it\". 😂\n", "\n", "As I say in the debugging guide - that's not how it works!\n", "\n", "When starting projects, it's crucial to start small and simple. Perfect every prompt; work in detail on every step. Satisfy yourself that each LLM call is performing as you wish, and iterate on the inputs until responses are consistent and reliable.\n", "\n", "If you always work incrementally and with small, testable building blocks, you should have complete clarity on what's going on.\n", "\n", "And of course, I love helping people with projects and it's great to help fix problems. But if you approach your project incrementally, you should always have clarity on exactly what isn't performing - and then it's super efficient for me to help you."]}, {"cell_type": "markdown", "metadata": {}, "source": ["# In summary\n", "\n", "You probably noticed the common theme through these 3 pieces of advice. It can feel jarring for people from a non-Data Science background to approach problems in such an ad-hoc way; it feels \"hacky\" and unsatisfying. But in my opinion, this is the single most important skill to acquire to be a successful AI engineer: being comfortable with uncertainty, enjoying the experiments, and embracing being a Scientist."]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}