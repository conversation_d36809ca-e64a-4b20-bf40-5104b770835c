{"cells": [{"cell_type": "markdown", "id": "5c291475-8c7c-461c-9b12-545a887b2432", "metadata": {}, "source": ["# Foundations of Python and fixing NameErrors\n", "\n", "## First, for a complete beginner\n", "\n", "This introductory tutorial gets you started with the basics.\n", "\n", "https://chatgpt.com/share/68063082-c2d8-8012-8d45-fa674aa1c1ed\n"]}, {"cell_type": "markdown", "id": "542f0577-a826-4613-a5d7-4170e9666d04", "metadata": {}, "source": ["## Next: a comprehensive briefing on Python\n", "\n", "I'm going to defer to an AI friend for this, because these explanations are so well written with great examples. Copy and paste the code examples into a new cell to give them a try. Pick whichever section(s) you'd like to brush up on.\n", "\n", "**Python imports:**  \n", "https://chatgpt.com/share/672f9f31-8114-8012-be09-29ef0d0140fb\n", "\n", "**Python functions** including default arguments:  \n", "https://chatgpt.com/share/672f9f99-7060-8012-bfec-46d4cf77d672\n", "\n", "**Python strings**, including slicing, split/join, replace and literals:  \n", "https://chatgpt.com/share/672fb526-0aa0-8012-9e00-ad1687c04518\n", "\n", "**Python f-strings** including number and date formatting:  \n", "https://chatgpt.com/share/672fa125-0de0-8012-8e35-27918cbb481c\n", "\n", "**Python lists, dicts and sets**, including the `get()` method:  \n", "https://chatgpt.com/share/672fa225-3f04-8012-91af-f9c95287da8d\n", "\n", "**Python files** including modes, encoding, context managers, Path, glob.glob:  \n", "https://chatgpt.com/share/673b53b2-6d5c-8012-a344-221056c2f960\n", "\n", "**Python classes:**  \n", "https://chatgpt.com/share/672fa07a-1014-8012-b2ea-6dc679552715\n", "\n", "**Pickling Python objects and converting to JSON:**  \n", "https://chatgpt.com/share/673b553e-9d0c-8012-9919-f3bb5aa23e31"]}, {"cell_type": "markdown", "id": "f9e0f8e1-09b3-478b-ada7-c8c35003929b", "metadata": {}, "source": ["## With this in mind - understanding NameErrors in Python\n", "\n", "It's quite common to hit a NameError in python. With foundational knowledge, you should always feel equipped to debug a NameError and get to the bottom of it.\n", "\n", "The most common reason if you're working in a Notebook is that you haven't executed all the cells in order, so the Kernel does not have something defined.\n", "\n", "If you're unsure how to fix a NameError, please see this [initial guide](https://chatgpt.com/share/67958312-ada0-8012-a1d3-62b3a5fcbbfc) and this [second guide with exercises](https://chatgpt.com/share/67a57e0b-0194-8012-bb50-8ea76c5995b8), and work through them both until you have high confidence.\n", "\n", "There's some repetition here, so feel free to skip it if you're already confident.\n", "\n", "After this, a NameError should never give you problems again!"]}, {"cell_type": "markdown", "id": "f465c23b", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}