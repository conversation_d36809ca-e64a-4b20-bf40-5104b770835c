{"cells": [{"cell_type": "markdown", "id": "5c291475-8c7c-461c-9b12-545a887b2432", "metadata": {}, "source": ["# Async Python\n", "\n", "## A briefing on asynchronous python coding, essential in Agent engineering"]}, {"cell_type": "markdown", "id": "538fa044", "metadata": {}, "source": ["Here is a masterful tutorial by you-know-who with exercises and comparisons.\n", "\n", "https://chatgpt.com/share/680648b1-b0a0-8012-8449-4f90b540886c\n", "\n", "This includes how to run async code from a python module.\n", "\n", "### And now some examples:"]}, {"cell_type": "code", "execution_count": 1, "id": "09f5662a", "metadata": {}, "outputs": [], "source": ["# Let's define an async function\n", "\n", "import asyncio\n", "\n", "async def do_some_work():\n", "    print(\"Starting work\")\n", "    await asyncio.sleep(1)\n", "    print(\"Work complete\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "07ab3abf", "metadata": {}, "outputs": [], "source": ["# What will this do?\n", "\n", "do_some_work()"]}, {"cell_type": "code", "execution_count": null, "id": "6d681b6d", "metadata": {}, "outputs": [], "source": ["# OK let's try that again!\n", "\n", "await do_some_work()"]}, {"cell_type": "code", "execution_count": null, "id": "ea867090", "metadata": {}, "outputs": [], "source": ["# What's wrong with this?\n", "\n", "async def do_a_lot_of_work():\n", "    do_some_work()\n", "    do_some_work()\n", "    do_some_work()\n", "\n", "await do_a_lot_of_work()"]}, {"cell_type": "code", "execution_count": null, "id": "e9c75c3f", "metadata": {}, "outputs": [], "source": ["# Interesting warning! Let's fix it\n", "\n", "async def do_a_lot_of_work():\n", "    await do_some_work()\n", "    await do_some_work()\n", "    await do_some_work()\n", "\n", "await do_a_lot_of_work()"]}, {"cell_type": "code", "execution_count": null, "id": "720cf3f5", "metadata": {}, "outputs": [], "source": ["# And now let's do it in parallel\n", "# It's important to recognize that this is not \"multi-threading\" in the way that you may be used to\n", "# The asyncio library is running on a single thread, but it's using a loop to switch between tasks while one is waiting\n", "\n", "async def do_a_lot_of_work_in_parallel():\n", "    await asyncio.gather(do_some_work(), do_some_work(), do_some_work())\n", "\n", "await do_a_lot_of_work_in_parallel()"]}, {"cell_type": "markdown", "id": "230f85de", "metadata": {}, "source": ["### Finally - try writing a python module that calls do_a_lot_of_work_in_parallel\n", "\n", "See the link at the top; you'll need something like this in your module:\n", "\n", "```python\n", "if __name__ == \"__main__\":\n", "    asyncio.run(do_a_lot_of_work_in_parallel())\n", "```"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}