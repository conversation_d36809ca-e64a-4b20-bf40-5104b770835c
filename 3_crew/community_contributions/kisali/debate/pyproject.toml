[project]
name = "debate"
version = "0.1.0"
description = "debate using crewA<PERSON>"
authors = [{ name = "Your Name", email = "<EMAIL>" }]
requires-python = ">=3.10,<3.14"
dependencies = [
    "crewai[tools]>=0.150.0,<1.0.0",
    "boto3>=1.34,<2.0"
]

[project.scripts]
debate = "debate.main:run"
run_crew = "debate.main:run"
train = "debate.main:train"
replay = "debate.main:replay"
test = "debate.main:test"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.crewai]
type = "crew"
