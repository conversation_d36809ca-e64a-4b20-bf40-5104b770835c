propose:
  description: >
    You are proposing the motion: {motion}.
    Come up with a clear argument in favor of the motion.
    Be very convincing.
  expected_output: >
    Your clear argument in favor of the motion, in a concise manner.
  agent: proposing_debater_agent
  output_file: output/propose.md

oppose:
  description: >
    You are opposing the motion: {motion}.
    Come up with a clear argument in favor of the motion.
    Be very convincing.
  expected_output: >
    Your clear argument opposing the motion, in a concise manner.
  agent: opposing_debater_agent
  output_file: output/oppose.md

decide:
  description: >
    Review the arguments presented by the debaters and decide which side is more convincing.
  expected_output: >
    Your decision on which side is more convincing, and why.
  agent: judge_agent
  output_file: output/decide.md