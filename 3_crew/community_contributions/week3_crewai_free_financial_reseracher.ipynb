{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["Install necessary libraries required to run the code in Google Colab"], "metadata": {"id": "kWO9784hz-Ky"}}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "mYU4obx8qPOX"}, "outputs": [], "source": ["!pip install crewai crewai_tools langchain openai langchain_community -- quiet"]}, {"cell_type": "markdown", "source": ["Get access to together AI free api key - https://api.together.xyz/models  & Serper API key - https://serper.dev/api-key"], "metadata": {"id": "qSh4wItL0yq_"}}, {"cell_type": "markdown", "source": ["## Financial Researcher - <PERSON><PERSON>"], "metadata": {"id": "-klDhYqyv8qP"}}, {"cell_type": "code", "source": ["from crewai_tools import SerperDevTool\n", "import os\n", "from crewai import LLM,Agent, Task, Crew\n", "from langchain.llms import OpenAI\n", "from google.colab import userdata\n", "# Set your OpenAI key\n", "\n", "os.environ[\"SERPER_API_KEY\"] = userdata.get('serper_api')\n", "\n", "llm = LLM(model=\"together_ai/deepseek-ai/DeepSeek-R1-Distill-Llama-70B-free\",\n", "          api_key=userdata.get('together_ai'),\n", "          base_url=\"https://api.together.xyz/v1\"\n", "        )\n", "\n", "research_agent = Agent(\n", "    llm = llm,\n", "    role=\"Senior Financial Researcher for {company}\",\n", "    goal=\"Research the company, news and potential for {company}\",\n", "    backstory=\"\"\"You're a seasoned financial researcher with a talent for finding\n", "    the most relevant information about {company}.\n", "    Known for your ability to find the most relevant\n", "    information and present it in a clear and concise manner.\"\"\",\n", "    tools=[SerperDevTool()],\n", "    verbose=True  # Enable logging for debugging\n", ")\n", "analyst_agent= Agent(\n", "    llm = llm,\n", "    role=\"Market Analyst and Report writer focused on {company}\",\n", "    goal=\"\"\" Analyze company {company} and create a comprehensive, well-structured report\n", "    that presents insights in a clear and engaging way\"\"\",\n", "    backstory=\"\"\" You're a meticulous, skilled analyst with a background in financial analysis\n", "    and company research. You have a talent for identifying patterns and extracting\n", "    meaningful insights from research data, then communicating\n", "    those insights through well crafted reports\"\"\",\n", "    tools=[SerperDevTool()],\n", "    verbose=True  # Enable logging for debugging\n", ")\n", "\n", "research_task = Task(\n", "    description=\"\"\"Conduct thorough research on company {company}. Focus on:\n", "    1. Current company status and health\n", "    2. Historical company performance\n", "    3. Major challenges and opportunities\n", "    4. Recent news and events\n", "    5. Future outlook and potential developments\n", "\n", "    Make sure to organize your findings in a structured format with clear sections.\"\"\",\n", "    expected_output=\"\"\" A comprehensive research document with well-organized sections covering\n", "    all the requested aspects of {company}. Include specific facts, figures,\n", "    and examples where relevant.\"\"\",\n", "    agent=research_agent\n", ")\n", "\n", "analysis_task = Task(\n", "    description=\"\"\"Analyze the research findings and create a comprehensive report on {company}.\n", "    Your report should:\n", "    1. <PERSON><PERSON> with an executive summary\n", "    2. Include all key information from the research\n", "    3. Provide insightful analysis of trends and patterns\n", "    4. Offer a market outlook for company, noting that this should not be used for trading decisions\n", "    5. Be formatted in a professional, easy-to-read style with clear headings\"\"\",\n", "    expected_output=\"\"\"  A polished, professional report on {company} that presents the research\n", "    findings with added analysis and insights. The report should be well-structured\n", "    with an executive summary, main sections, and conclusion.\"\"\",\n", "    agent=analyst_agent\n", ")\n", "\n", "# Execute the crew\n", "crew = Crew(\n", "    agents=[research_agent,analyst_agent],\n", "    tasks=[research_task,analysis_task],\n", "    verbose=True\n", ")\n"], "metadata": {"id": "gulD7es5wYg0"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["inputs = {\n", "        'company': 'Apple'\n", "    }\n", "\n", "result = crew.kickoff(inputs=inputs)"], "metadata": {"id": "7CXfoRjjwAB_"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["print(result.raw)"], "metadata": {"id": "dwDRsHDUzWt6", "collapsed": true}, "execution_count": null, "outputs": []}]}