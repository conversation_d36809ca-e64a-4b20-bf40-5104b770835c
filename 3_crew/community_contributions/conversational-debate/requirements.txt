# This file was autogenerated by uv via the following command:
#    uv pip compile pyproject.toml -o requirements.txt
aiofiles==24.1.0
    # via gradio
aiohappyeyeballs==2.6.1
    # via aiohttp
aiohttp==3.12.14
    # via
    #   instructor
    #   litellm
aiosignal==1.4.0
    # via aiohttp
annotated-types==0.7.0
    # via pydantic
anyio==4.9.0
    # via
    #   gradio
    #   httpx
    #   openai
    #   starlette
    #   watchfiles
appdirs==1.4.4
    # via crewai
asttokens==3.0.0
    # via stack-data
attrs==25.3.0
    # via
    #   aiohttp
    #   jsonschema
    #   referencing
babel==2.17.0
    # via mkdocs-material
backoff==2.2.1
    # via posthog
backrefs==5.9
    # via mkdocs-material
bcrypt==4.3.0
    # via chromadb
blinker==1.9.0
    # via crewai
brotli==1.1.0
    # via gradio
build==1.2.2.post1
    # via chromadb
cachetools==5.5.2
    # via google-auth
certifi==2025.7.14
    # via
    #   httpcore
    #   httpx
    #   kubernetes
    #   requests
cffi==1.17.1
    # via cryptography
cfgv==3.4.0
    # via pre-commit
charset-normalizer==3.4.2
    # via
    #   pdfminer-six
    #   requests
chromadb==1.0.15
    # via
    #   debate-prep-ai (pyproject.toml)
    #   crewai
click==8.2.1
    # via
    #   crewai
    #   litellm
    #   mkdocs
    #   typer
    #   uvicorn
colorama==0.4.6
    # via
    #   build
    #   click
    #   ipython
    #   mkdocs
    #   mkdocs-material
    #   tqdm
    #   uvicorn
coloredlogs==15.0.1
    # via onnxruntime
crewai==0.148.0
    # via debate-prep-ai (pyproject.toml)
cryptography==45.0.5
    # via pdfminer-six
decorator==5.2.1
    # via ipython
distlib==0.4.0
    # via virtualenv
distro==1.9.0
    # via
    #   openai
    #   posthog
docstring-parser==0.16
    # via instructor
dotenv==0.9.9
    # via debate-prep-ai (pyproject.toml)
durationpy==0.10
    # via kubernetes
et-xmlfile==2.0.0
    # via openpyxl
executing==2.2.0
    # via stack-data
fastapi==0.116.1
    # via gradio
ffmpy==0.6.0
    # via gradio
filelock==3.18.0
    # via
    #   huggingface-hub
    #   virtualenv
flatbuffers==25.2.10
    # via onnxruntime
frozenlist==1.7.0
    # via
    #   aiohttp
    #   aiosignal
fsspec==2025.7.0
    # via
    #   gradio-client
    #   huggingface-hub
ghp-import==2.1.0
    # via mkdocs
google-auth==2.40.3
    # via kubernetes
googleapis-common-protos==1.70.0
    # via
    #   opentelemetry-exporter-otlp-proto-grpc
    #   opentelemetry-exporter-otlp-proto-http
gradio==5.38.0
    # via debate-prep-ai (pyproject.toml)
gradio-client==1.11.0
    # via gradio
groovy==0.1.2
    # via gradio
grpcio==1.73.1
    # via
    #   chromadb
    #   opentelemetry-exporter-otlp-proto-grpc
h11==0.16.0
    # via
    #   httpcore
    #   uvicorn
httpcore==1.0.9
    # via httpx
httptools==0.6.4
    # via uvicorn
httpx==0.28.1
    # via
    #   chromadb
    #   gradio
    #   gradio-client
    #   litellm
    #   openai
    #   safehttpx
huggingface-hub==0.33.4
    # via
    #   gradio
    #   gradio-client
    #   tokenizers
humanfriendly==10.0
    # via coloredlogs
identify==2.6.12
    # via pre-commit
idna==3.10
    # via
    #   anyio
    #   httpx
    #   requests
    #   yarl
importlib-metadata==8.7.0
    # via
    #   litellm
    #   opentelemetry-api
importlib-resources==6.5.2
    # via chromadb
instructor==1.9.2
    # via crewai
ipython==9.4.0
    # via pyvis
ipython-pygments-lexers==1.1.1
    # via ipython
jedi==0.19.2
    # via ipython
jinja2==3.1.6
    # via
    #   gradio
    #   instructor
    #   litellm
    #   mkdocs
    #   mkdocs-material
    #   pyvis
jiter==0.10.0
    # via
    #   instructor
    #   openai
json-repair==0.25.2
    # via crewai
json5==0.12.0
    # via crewai
jsonpickle==4.1.1
    # via pyvis
jsonref==1.1.0
    # via crewai
jsonschema==4.24.1
    # via
    #   chromadb
    #   litellm
jsonschema-specifications==2025.4.1
    # via jsonschema
kubernetes==33.1.0
    # via chromadb
litellm==1.72.6
    # via crewai
markdown==3.8.2
    # via
    #   mkdocs
    #   mkdocs-material
    #   pymdown-extensions
markdown-it-py==3.0.0
    # via rich
markupsafe==3.0.2
    # via
    #   gradio
    #   jinja2
    #   mkdocs
matplotlib-inline==0.1.7
    # via ipython
mdurl==0.1.2
    # via markdown-it-py
mergedeep==1.3.4
    # via
    #   mkdocs
    #   mkdocs-get-deps
mkdocs==1.6.1
    # via
    #   instructor
    #   mkdocs-material
mkdocs-get-deps==0.2.0
    # via mkdocs
mkdocs-material==9.6.15
    # via instructor
mkdocs-material-extensions==1.3.1
    # via mkdocs-material
mmh3==5.1.0
    # via chromadb
mpmath==1.3.0
    # via sympy
multidict==6.6.3
    # via
    #   aiohttp
    #   yarl
networkx==3.5
    # via pyvis
nodeenv==1.9.1
    # via pre-commit
numpy==2.3.1
    # via
    #   chromadb
    #   gradio
    #   onnxruntime
    #   pandas
oauthlib==3.3.1
    # via
    #   kubernetes
    #   requests-oauthlib
onnxruntime==1.22.0
    # via
    #   chromadb
    #   crewai
openai==1.97.0
    # via
    #   crewai
    #   instructor
    #   litellm
openpyxl==3.1.5
    # via crewai
opentelemetry-api==1.35.0
    # via
    #   chromadb
    #   crewai
    #   opentelemetry-exporter-otlp-proto-grpc
    #   opentelemetry-exporter-otlp-proto-http
    #   opentelemetry-sdk
    #   opentelemetry-semantic-conventions
opentelemetry-exporter-otlp-proto-common==1.35.0
    # via
    #   opentelemetry-exporter-otlp-proto-grpc
    #   opentelemetry-exporter-otlp-proto-http
opentelemetry-exporter-otlp-proto-grpc==1.35.0
    # via chromadb
opentelemetry-exporter-otlp-proto-http==1.35.0
    # via crewai
opentelemetry-proto==1.35.0
    # via
    #   opentelemetry-exporter-otlp-proto-common
    #   opentelemetry-exporter-otlp-proto-grpc
    #   opentelemetry-exporter-otlp-proto-http
opentelemetry-sdk==1.35.0
    # via
    #   chromadb
    #   crewai
    #   opentelemetry-exporter-otlp-proto-grpc
    #   opentelemetry-exporter-otlp-proto-http
opentelemetry-semantic-conventions==0.56b0
    # via opentelemetry-sdk
orjson==3.11.0
    # via
    #   chromadb
    #   gradio
overrides==7.7.0
    # via chromadb
packaging==25.0
    # via
    #   build
    #   gradio
    #   gradio-client
    #   huggingface-hub
    #   mkdocs
    #   onnxruntime
paginate==0.5.7
    # via mkdocs-material
pandas==2.3.1
    # via gradio
parso==0.8.4
    # via jedi
pathspec==0.12.1
    # via mkdocs
pdfminer-six==20250506
    # via pdfplumber
pdfplumber==0.11.7
    # via crewai
pillow==11.3.0
    # via
    #   gradio
    #   pdfplumber
platformdirs==4.3.8
    # via
    #   mkdocs-get-deps
    #   virtualenv
posthog==5.4.0
    # via chromadb
pre-commit==4.2.0
    # via instructor
prompt-toolkit==3.0.51
    # via ipython
propcache==0.3.2
    # via
    #   aiohttp
    #   yarl
protobuf==6.31.1
    # via
    #   googleapis-common-protos
    #   onnxruntime
    #   opentelemetry-proto
pure-eval==0.2.3
    # via stack-data
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.2
    # via google-auth
pybase64==1.4.1
    # via chromadb
pycparser==2.22
    # via cffi
pydantic==2.11.7
    # via
    #   chromadb
    #   crewai
    #   fastapi
    #   gradio
    #   instructor
    #   litellm
    #   openai
pydantic-core==2.33.2
    # via
    #   instructor
    #   pydantic
pydub==0.25.1
    # via gradio
pygments==2.19.2
    # via
    #   ipython
    #   ipython-pygments-lexers
    #   mkdocs-material
    #   rich
pyjwt==2.10.1
    # via crewai
pymdown-extensions==10.16
    # via mkdocs-material
pypdfium2==4.30.1
    # via pdfplumber
pypika==0.48.9
    # via chromadb
pyproject-hooks==1.2.0
    # via build
pyreadline3==3.5.4
    # via humanfriendly
python-dateutil==2.9.0.post0
    # via
    #   ghp-import
    #   kubernetes
    #   pandas
    #   posthog
python-dotenv==1.1.1
    # via
    #   crewai
    #   dotenv
    #   litellm
    #   uvicorn
python-multipart==0.0.20
    # via gradio
pytz==2025.2
    # via pandas
pyvis==0.3.2
    # via crewai
pyyaml==6.0.2
    # via
    #   chromadb
    #   gradio
    #   huggingface-hub
    #   kubernetes
    #   mkdocs
    #   mkdocs-get-deps
    #   pre-commit
    #   pymdown-extensions
    #   pyyaml-env-tag
    #   uvicorn
pyyaml-env-tag==1.1
    # via mkdocs
referencing==0.36.2
    # via
    #   jsonschema
    #   jsonschema-specifications
regex==2024.11.6
    # via
    #   crewai
    #   tiktoken
requests==2.32.4
    # via
    #   huggingface-hub
    #   instructor
    #   kubernetes
    #   mkdocs-material
    #   opentelemetry-exporter-otlp-proto-http
    #   posthog
    #   requests-oauthlib
    #   tiktoken
requests-oauthlib==2.0.0
    # via kubernetes
rich==14.0.0
    # via
    #   chromadb
    #   instructor
    #   typer
rpds-py==0.26.0
    # via
    #   jsonschema
    #   referencing
rsa==4.9.1
    # via google-auth
ruff==0.12.4
    # via gradio
safehttpx==0.1.6
    # via gradio
semantic-version==2.10.0
    # via gradio
shellingham==1.5.4
    # via typer
six==1.17.0
    # via
    #   kubernetes
    #   posthog
    #   python-dateutil
sniffio==1.3.1
    # via
    #   anyio
    #   openai
stack-data==0.6.3
    # via ipython
starlette==0.47.1
    # via
    #   fastapi
    #   gradio
sympy==1.14.0
    # via onnxruntime
tenacity==9.1.2
    # via
    #   chromadb
    #   instructor
tiktoken==0.9.0
    # via litellm
tokenizers==0.21.2
    # via
    #   chromadb
    #   crewai
    #   litellm
tomli==2.2.1
    # via crewai
tomli-w==1.2.0
    # via crewai
tomlkit==0.13.3
    # via gradio
tqdm==4.67.1
    # via
    #   chromadb
    #   huggingface-hub
    #   openai
traitlets==5.14.3
    # via
    #   ipython
    #   matplotlib-inline
typer==0.16.0
    # via
    #   chromadb
    #   gradio
    #   instructor
typing-extensions==4.14.1
    # via
    #   aiosignal
    #   anyio
    #   chromadb
    #   fastapi
    #   gradio
    #   gradio-client
    #   huggingface-hub
    #   openai
    #   opentelemetry-api
    #   opentelemetry-exporter-otlp-proto-grpc
    #   opentelemetry-exporter-otlp-proto-http
    #   opentelemetry-sdk
    #   opentelemetry-semantic-conventions
    #   pydantic
    #   pydantic-core
    #   referencing
    #   starlette
    #   typer
    #   typing-inspection
typing-inspection==0.4.1
    # via pydantic
tzdata==2025.2
    # via pandas
urllib3==2.5.0
    # via
    #   kubernetes
    #   requests
uv==0.8.0
    # via crewai
uvicorn==0.35.0
    # via
    #   chromadb
    #   gradio
virtualenv==20.31.2
    # via pre-commit
watchdog==6.0.0
    # via mkdocs
watchfiles==1.1.0
    # via uvicorn
wcwidth==0.2.13
    # via prompt-toolkit
websocket-client==1.8.0
    # via kubernetes
websockets==15.0.1
    # via
    #   gradio-client
    #   uvicorn
yarl==1.20.1
    # via aiohttp
zipp==3.23.0
    # via importlib-metadata
