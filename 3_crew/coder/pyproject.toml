[project]
name = "coder"
version = "0.1.0"
description = "coder using crewA<PERSON>"
authors = [{ name = "Your Name", email = "<EMAIL>" }]
requires-python = ">=3.10,<3.13"
dependencies = [
    "crewai[tools]>=0.108.0,<1.0.0",
    "gradio>=5.23.3",
]

[project.scripts]
coder = "coder.main:run"
run_crew = "coder.main:run"
train = "coder.main:train"
replay = "coder.main:replay"
test = "coder.main:test"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.crewai]
type = "crew"
