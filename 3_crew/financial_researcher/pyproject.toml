[project]
name = "financial_researcher"
version = "0.1.0"
description = "financial_researcher using crewAI"
authors = [{ name = "Your Name", email = "<EMAIL>" }]
requires-python = ">=3.10,<3.13"
dependencies = [
    "crewai[tools]>=0.108.0,<1.0.0"
]

[project.scripts]
financial_researcher = "financial_researcher.main:run"
run_crew = "financial_researcher.main:run"
train = "financial_researcher.main:train"
replay = "financial_researcher.main:replay"
test = "financial_researcher.main:test"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.crewai]
type = "crew"
