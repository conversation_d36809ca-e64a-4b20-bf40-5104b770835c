[project]
name = "engineering_team"
version = "0.1.0"
description = "engineering_team using crewAI"
authors = [{ name = "Your Name", email = "<EMAIL>" }]
requires-python = ">=3.10,<3.13"
dependencies = [
    "crewai[tools]>=0.108.0,<1.0.0",
    "gradio>=5.22.0",
]

[project.scripts]
engineering_team = "engineering_team.main:run"
run_crew = "engineering_team.main:run"
train = "engineering_team.main:train"
replay = "engineering_team.main:replay"
test = "engineering_team.main:test"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.crewai]
type = "crew"
