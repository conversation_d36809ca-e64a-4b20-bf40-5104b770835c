{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Agent logging with lifecycle hooks\n", "\n", "This notebook demonstrates using OpenAI's Agent lifecycle hooks to print the steps the agent is taking to solve the task.\n", "\n", "Based on the [agent lifecycle example](https://github.com/openai/openai-agents-python/blob/main/examples/basic/agent_lifecycle_example.py) in the openai agents examples.\n", "\n", "This gives us some idea of what is happening without having to use the traces screen.\n", "\n", "It also demonstrates using a class to instantiate multiple MCP servers from multiple configs, which makes using several MCP servers at once more ergonomic in a notebook."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["# The imports\n", "\n", "from dotenv import load_dotenv\n", "from agents import Agent, Runner, trace\n", "from agents_logging import AgentLoggingHooks\n", "from mcp_server_manager import MCPServers\n", "import os"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["load_dotenv(override=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["sandbox_path = os.path.abspath(os.path.join(os.getcwd(), \"sandbox\"))\n", "brave_api_key = os.getenv(\"BRAVE_API_KEY\")\n", "\n", "server_configs = {\n", "    'files': {'params': {\"command\": \"npx\", \"args\": [\"-y\", \"@modelcontextprotocol/server-filesystem\", sandbox_path]}},\n", "    'browser': {'params': {\"command\": \"npx\", \"args\": [\"@playwright/mcp@latest\"]}},\n", "    'brave': {'params': {\"command\": \"npx\", \"args\": [\"-y\", \"@modelcontextprotocol/server-brave-search\"], \"env\": {\"BRAVE_API_KEY\": brave_api_key}}}\n", "}\n", "\n", "\n", "instructions = \"\"\"\n", "You browse the internet to accomplish your instructions.\n", "You are highly capable at browsing the internet independently to accomplish your task, \n", "including accepting all cookies and clicking 'not now' as\n", "appropriate to get to the content you need. If one website isn't fruitful, try another. \n", "Be persistent until you have solved your assignment,\n", "trying different options and sites as needed.\n", "You search the web to find good information, and then use the web URLs to navigate to the content you need.\n", "You can save files to the sandbox, and you can read files from the sandbox.\n", "If you save the user's request to the sandbox, you say so, rather than replying with the file content or a summary of the file content.\n", "\"\"\"\n", "\n", "input = \"\"\"\n", "I want a good recipe for a 500g loaf of sourdough bread.\n", "Use a search and find the best recipe from the results.\n", "Then use your browser automation tools navigate to that recipe via its URL.\n", "From the page full content, give a detailed summary of the recipe and save it to sandbox/recipe.md in markdown format.\n", "If a page doesn't load, try another one.\n", "\"\"\"\n", "\n", "\n", "async with MCPServers(server_configs) as mcp:\n", "    agent = Agent(\n", "        name=\"Researcher\", \n", "        instructions=instructions, \n", "        model=\"gpt-4o-mini\",\n", "        mcp_servers=mcp.get_all_servers(),\n", "        hooks=AgentLoggingHooks(display_name=\"Researcher\")\n", "        )\n", "    with trace(\"investigate\"):\n", "        result = await Runner.run(agent, input)\n", "        print(\"\\n\\n=== FINAL OUTPUT ===\\n\\n\")\n", "        print(result.final_output)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Check out the trace\n", "\n", "https://platform.openai.com/traces"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}