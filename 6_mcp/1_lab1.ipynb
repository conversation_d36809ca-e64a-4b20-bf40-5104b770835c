{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## WELCOME TO WEEK 6\n", "\n", "The Epic Finale Week\n", "\n", "And\n", "\n", "# WELCOME TO THE **M**ODEL **C**ONTEXT **P**ROTOCOL!\n", "\n", "And welcome back to OpenAI Agents SDK ❤️❤️❤️\n", "\n", "### Please note\n", "\n", "There may be changes here from the video as I'm always making updates!"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../assets/stop.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#ff7800;\">To my Windows PC people - an important announcement</h2>\n", "            <span style=\"color:#ff7800;\">I have unpleasant news. There's a problem running MCP Servers on Windows PCs; Mac and Linux is fine. This is a known issue as of May 4th, 2025. I asked o3 with Deep Research to try to find workarounds; it <a href=\"https://chatgpt.com/share/6817bbc3-3d0c-8012-9b51-631842470628\">confirmed the issue</a> and confirmed the workaround.<br/><br/>\n", "            The workaround is a bit of a bore. It is to take advantage of \"WSL\", the Microsoft approach for running Linux on your PC. You'll need to carry out more setup instructions! But it's quick, and several students have confirmed that this works perfectly for them, then the Week 6 MCP labs work. Plus, WSL is actually a great way to build software on your Windows PC.<br/>\n", "            The WSL Setup instructions are in the Setup folder, <a href=\"../setup/SETUP-WSL.md\">in the file called SETUP-WSL.md here</a>. I do hope this only holds you up briefly - you should be back up and running quickly. Oh the joys of working with bleeding-edge technology!<br/><br/>\n", "            With many thanks to students <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and several others, for helping me work on it and confirming the fix.\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# The imports\n", "\n", "from dotenv import load_dotenv\n", "from agents import Agent, Runner, trace\n", "from agents.mcp import MCPServerStdio\n", "import os"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["load_dotenv(override=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Let's use MCP in OpenAI Agents SDK\n", "\n", "1. Create a Client\n", "\n", "2. Have it spawn a server\n", "\n", "3. Collect the tools that the server can use\n", "\n", "Let's try the Fetch mcp-server that we looked at last week\n", "\n", "### Special note - in case you updated the OpenAI Agents framework:\n", "\n", "OpenAI Agents SDK recently upgraded their SDK and made a breaking change (thank you, OpenAI 😂). I haven't yet updated the version in this repo to the latest version. If you've upgraded the package yourself, then you might get an error about missing positional arguments. If you get that error, you'll need to replace `await server.list_tools()` with `await server.session.list_tools()` and change the last line from `fetch_tools` to `fetch_tools.tools`. And you'll need to make this change every time you get this error! The joys of working at the bleeding edge.."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fetch_params = {\"command\": \"uvx\", \"args\": [\"mcp-server-fetch\"]}\n", "\n", "async with MCPServerStdio(params=fetch_params, client_session_timeout_seconds=60) as server:\n", "    fetch_tools = await server.list_tools()\n", "\n", "fetch_tools"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Extra installation step - if you don't have \"node\" on your computer\n", "\n", "The next MCP tool uses node (the Javascript Server), and it needs you to have the command 'npx' installed on your computer.\n", "\n", "You may already have this, but if not, here are super clear instructions on exactly what to do, courtesy of our friend.  \n", "And thank you to student avid_learner for pointing this out.\n", "\n", "https://chatgpt.com/share/68103af2-e2dc-8012-b259-bc135a23273b\n", "\n", "**Windows Users take note:** node needs to be installed on your WSL platform, rather than your Windows side.  \n", "And some windows users have mentioned that they needed to replace \"npx\" below with a full path to npx to get this to work properly.."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### And now repeat for 2 more!\n", "\n", "The first one is to be able to run <PERSON><PERSON>.\n", "\n", "It's possible that you will need to run these commands in a Terminal for this to work:\n", "\n", "On Mac:  \n", "`playwright install`\n", "\n", "On Linux / WSL:  \n", "`playwright install --with-deps chromium`\n", "\n", "Student <PERSON> mentioned that you might also need to do this in a cursor terminal:\n", "\n", "`uv run playwright install chromium`"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["playwright_params = {\"command\": \"npx\",\"args\": [ \"@playwright/mcp@latest\"]}\n", "\n", "async with MCPServerStdio(params=playwright_params, client_session_timeout_seconds=60) as server:\n", "    playwright_tools = await server.list_tools()\n", "\n", "playwright_tools\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "sandbox_path = os.path.abspath(os.path.join(os.getcwd(), \"sandbox\"))\n", "files_params = {\"command\": \"npx\", \"args\": [\"-y\", \"@modelcontextprotocol/server-filesystem\", sandbox_path]}\n", "\n", "async with MCPServerStdio(params=files_params,client_session_timeout_seconds=60) as server:\n", "    file_tools = await server.list_tools()\n", "\n", "file_tools"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### And now.. bring on the <PERSON> with Tools!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["instructions = \"\"\"\n", "You browse the internet to accomplish your instructions.\n", "You are highly capable at browsing the internet independently to accomplish your task, \n", "including accepting all cookies and clicking 'not now' as\n", "appropriate to get to the content you need. If one website isn't fruitful, try another. \n", "Be persistent until you have solved your assignment,\n", "trying different options and sites as needed.\n", "\"\"\"\n", "\n", "\n", "async with MCPServerStdio(params=files_params, client_session_timeout_seconds=60) as mcp_server_files:\n", "    async with MCPServerStdio(params=playwright_params, client_session_timeout_seconds=60) as mcp_server_browser:\n", "        agent = Agent(\n", "            name=\"investigator\", \n", "            instructions=instructions, \n", "            model=\"gpt-4.1-mini\",\n", "            mcp_servers=[mcp_server_files, mcp_server_browser]\n", "            )\n", "        with trace(\"investigate\"):\n", "            result = await <PERSON>.run(agent, \"Find a great recipe for Banoffee Pie, then summarize it in markdown to banoffee.md\")\n", "            print(result.final_output)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Check out the trace\n", "\n", "https://platform.openai.com/traces\n", "\n", "### Now take a look at some MCP marketplaces\n", "\n", "https://mcp.so\n", "\n", "https://glama.ai/mcp\n", "\n", "https://smithery.ai/\n", "\n", "https://huggingface.co/blog/LLMhacker/top-11-essential-mcp-libraries\n", "\n", "HuggingFace great community article:\n", "https://huggingface.co/blog/Kseniase/mcp\n", "\n", "\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}