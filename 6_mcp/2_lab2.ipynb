{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["### Week 6, Day 2\n", "\n", "We're about to create and use our own MCP Server and MCP Client!\n", "\n", "It's pretty simple, but it's not super-simple. The excitment around MCP is about how easy it is to share and use other MCP Servers - making our own does involve a bit of work.\n", "\n", "Let's review some python code made mostly by a hard-working Engineering Team:\n", "\n", "accounts.py"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["from dotenv import load_dotenv\n", "from agents import Agent, Runner, trace\n", "from agents.mcp import MCPServerStdio\n", "from IPython.display import display, Markdown\n", "\n", "load_dotenv(override=True)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["from accounts import Account"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["account = Account.get(\"Ed\")\n", "account"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["account.buy_shares(\"AMZN\", 3, \"Because this bookstore website looks promising\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["account.report()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["account.list_transactions()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Now we write an MCP server and use it directly!"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Now let's use our accounts server as an MCP server\n", "\n", "params = {\"command\": \"uv\", \"args\": [\"run\", \"accounts_server.py\"]}\n", "async with MCPServerStdio(params=params, client_session_timeout_seconds=30) as server:\n", "    mcp_tools = await server.list_tools()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mcp_tools"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["instructions = \"You are able to manage an account for a client, and answer questions about the account.\"\n", "request = \"My name is <PERSON> and my account is under the name <PERSON>. What's my balance and my holdings?\"\n", "model = \"gpt-4.1-mini\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "async with MCPServerStdio(params=params, client_session_timeout_seconds=30) as mcp_server:\n", "    agent = Agent(name=\"account_manager\", instructions=instructions, model=model, mcp_servers=[mcp_server])\n", "    with trace(\"account_manager\"):\n", "        result = await Runner.run(agent, request)\n", "    display(Markdown(result.final_output))\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Now let's build our own MCP Client"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[Tool(name='get_balance', description='Get the cash balance of the given account name.\\n\\n    Args:\\n        name: The name of the account holder\\n    ', inputSchema={'properties': {'name': {'title': 'Name', 'type': 'string'}}, 'required': ['name'], 'title': 'get_balanceArguments', 'type': 'object'}, annotations=None), Tool(name='get_holdings', description='Get the holdings of the given account name.\\n\\n    Args:\\n        name: The name of the account holder\\n    ', inputSchema={'properties': {'name': {'title': 'Name', 'type': 'string'}}, 'required': ['name'], 'title': 'get_holdingsArguments', 'type': 'object'}, annotations=None), Tool(name='buy_shares', description=\"Buy shares of a stock.\\n\\n    Args:\\n        name: The name of the account holder\\n        symbol: The symbol of the stock\\n        quantity: The quantity of shares to buy\\n        rationale: The rationale for the purchase and fit with the account's strategy\\n    \", inputSchema={'properties': {'name': {'title': 'Name', 'type': 'string'}, 'symbol': {'title': 'Symbol', 'type': 'string'}, 'quantity': {'title': 'Quantity', 'type': 'integer'}, 'rationale': {'title': 'Rationale', 'type': 'string'}}, 'required': ['name', 'symbol', 'quantity', 'rationale'], 'title': 'buy_sharesArguments', 'type': 'object'}, annotations=None), Tool(name='sell_shares', description=\"Sell shares of a stock.\\n\\n    Args:\\n        name: The name of the account holder\\n        symbol: The symbol of the stock\\n        quantity: The quantity of shares to sell\\n        rationale: The rationale for the sale and fit with the account's strategy\\n    \", inputSchema={'properties': {'name': {'title': 'Name', 'type': 'string'}, 'symbol': {'title': 'Symbol', 'type': 'string'}, 'quantity': {'title': 'Quantity', 'type': 'integer'}, 'rationale': {'title': 'Rationale', 'type': 'string'}}, 'required': ['name', 'symbol', 'quantity', 'rationale'], 'title': 'sell_sharesArguments', 'type': 'object'}, annotations=None), Tool(name='change_strategy', description='At your discretion, if you choose to, call this to change your investment strategy for the future.\\n\\n    Args:\\n        name: The name of the account holder\\n        strategy: The new strategy for the account\\n    ', inputSchema={'properties': {'name': {'title': 'Name', 'type': 'string'}, 'strategy': {'title': 'Strategy', 'type': 'string'}}, 'required': ['name', 'strategy'], 'title': 'change_strategyArguments', 'type': 'object'}, annotations=None)]\n", "[FunctionTool(name='get_balance', description='Get the cash balance of the given account name.\\n\\n    Args:\\n        name: The name of the account holder\\n    ', params_json_schema={'properties': {'name': {'title': 'Name', 'type': 'string'}}, 'required': ['name'], 'title': 'get_balanceArguments', 'type': 'object', 'additionalProperties': False}, on_invoke_tool=<function get_accounts_tools_openai.<locals>.<lambda> at 0x114e13e20>, strict_json_schema=True), FunctionTool(name='get_holdings', description='Get the holdings of the given account name.\\n\\n    Args:\\n        name: The name of the account holder\\n    ', params_json_schema={'properties': {'name': {'title': 'Name', 'type': 'string'}}, 'required': ['name'], 'title': 'get_holdingsArguments', 'type': 'object', 'additionalProperties': False}, on_invoke_tool=<function get_accounts_tools_openai.<locals>.<lambda> at 0x1120860c0>, strict_json_schema=True), FunctionTool(name='buy_shares', description=\"Buy shares of a stock.\\n\\n    Args:\\n        name: The name of the account holder\\n        symbol: The symbol of the stock\\n        quantity: The quantity of shares to buy\\n        rationale: The rationale for the purchase and fit with the account's strategy\\n    \", params_json_schema={'properties': {'name': {'title': 'Name', 'type': 'string'}, 'symbol': {'title': 'Symbol', 'type': 'string'}, 'quantity': {'title': 'Quantity', 'type': 'integer'}, 'rationale': {'title': 'Rationale', 'type': 'string'}}, 'required': ['name', 'symbol', 'quantity', 'rationale'], 'title': 'buy_sharesArguments', 'type': 'object', 'additionalProperties': False}, on_invoke_tool=<function get_accounts_tools_openai.<locals>.<lambda> at 0x112086020>, strict_json_schema=True), FunctionTool(name='sell_shares', description=\"Sell shares of a stock.\\n\\n    Args:\\n        name: The name of the account holder\\n        symbol: The symbol of the stock\\n        quantity: The quantity of shares to sell\\n        rationale: The rationale for the sale and fit with the account's strategy\\n    \", params_json_schema={'properties': {'name': {'title': 'Name', 'type': 'string'}, 'symbol': {'title': 'Symbol', 'type': 'string'}, 'quantity': {'title': 'Quantity', 'type': 'integer'}, 'rationale': {'title': 'Rationale', 'type': 'string'}}, 'required': ['name', 'symbol', 'quantity', 'rationale'], 'title': 'sell_sharesArguments', 'type': 'object', 'additionalProperties': False}, on_invoke_tool=<function get_accounts_tools_openai.<locals>.<lambda> at 0x114e7a020>, strict_json_schema=True), FunctionTool(name='change_strategy', description='At your discretion, if you choose to, call this to change your investment strategy for the future.\\n\\n    Args:\\n        name: The name of the account holder\\n        strategy: The new strategy for the account\\n    ', params_json_schema={'properties': {'name': {'title': 'Name', 'type': 'string'}, 'strategy': {'title': 'Strategy', 'type': 'string'}}, 'required': ['name', 'strategy'], 'title': 'change_strategyArguments', 'type': 'object', 'additionalProperties': False}, on_invoke_tool=<function get_accounts_tools_openai.<locals>.<lambda> at 0x114e7a340>, strict_json_schema=True)]\n"]}], "source": ["from accounts_client import get_accounts_tools_openai, read_accounts_resource, list_accounts_tools\n", "\n", "mcp_tools = await list_accounts_tools()\n", "print(mcp_tools)\n", "openai_tools = await get_accounts_tools_openai()\n", "print(openai_tools)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/markdown": ["Ed, your current cash balance is $8,826.64. Is there anything else you would like to know or do with your account?"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["request = \"My name is <PERSON> and my account is under the name <PERSON>. What's my balance?\"\n", "\n", "with trace(\"account_mcp_client\"):\n", "    agent = Agent(name=\"account_manager\", instructions=instructions, model=model, tools=openai_tools)\n", "    result = await Runner.run(agent, request)\n", "    display(Markdown(result.final_output))"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\"name\": \"ed\", \"balance\": 8826.63796, \"strategy\": \"\", \"holdings\": {\"AMZN\": 6}, \"transactions\": [{\"symbol\": \"AMZN\", \"quantity\": 3, \"price\": 190.35996, \"timestamp\": \"2025-05-04 09:23:25\", \"rationale\": \"Because this bookstore website looks promising\"}, {\"symbol\": \"AMZN\", \"quantity\": 3, \"price\": 200.**************, \"timestamp\": \"2025-05-25 13:06:00\", \"rationale\": \"Because this bookstore website looks promising\"}], \"portfolio_value_time_series\": [[\"2025-05-04 09:23:25\", 9998.************], [\"2025-05-25 13:06:00\", 10028.79796], [\"2025-05-25 13:06:10\", 10028.79796], [\"2025-05-25 14:33:31\", 10028.79796], [\"2025-05-25 14:33:48\", 10028.79796], [\"2025-05-25 14:38:26\", 10028.79796]], \"total_portfolio_value\": 10028.79796, \"total_profit_loss\": 28.***************}\n"]}], "source": ["context = await read_accounts_resource(\"ed\")\n", "print(context)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["'{\"name\": \"ed\", \"balance\": 8826.63796, \"strategy\": \"\", \"holdings\": {\"AMZN\": 6}, \"transactions\": [{\"symbol\": \"AMZN\", \"quantity\": 3, \"price\": 190.35996, \"timestamp\": \"2025-05-04 09:23:25\", \"rationale\": \"Because this bookstore website looks promising\"}, {\"symbol\": \"AMZN\", \"quantity\": 3, \"price\": 200.**************, \"timestamp\": \"2025-05-25 13:06:00\", \"rationale\": \"Because this bookstore website looks promising\"}], \"portfolio_value_time_series\": [[\"2025-05-04 09:23:25\", 9998.************], [\"2025-05-25 13:06:00\", 10028.79796], [\"2025-05-25 13:06:10\", 10028.79796], [\"2025-05-25 14:33:31\", 10028.79796], [\"2025-05-25 14:33:48\", 10028.79796], [\"2025-05-25 14:38:26\", 10028.79796], [\"2025-05-25 14:38:41\", 10028.79796]], \"total_portfolio_value\": 10028.79796, \"total_profit_loss\": 28.***************}'"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["from accounts import Account\n", "Account.get(\"ed\").report()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<table style=\"margin: 0; text-align: left; width:100%\">\n", "    <tr>\n", "        <td style=\"width: 150px; height: 150px; vertical-align: middle;\">\n", "            <img src=\"../assets/exercise.png\" width=\"150\" height=\"150\" style=\"display: block;\" />\n", "        </td>\n", "        <td>\n", "            <h2 style=\"color:#ff7800;\">Exercises</h2>\n", "            <span style=\"color:#ff7800;\">Make your own MCP Server! Make a simple function to return the current Date, and expose it as a tool so that an Agent can tell you today's date.<br/>Harder optional exercise: then make an MCP Client, and use a native OpenAI call (without the Agents SDK) to use your tool via your client.\n", "            </span>\n", "        </td>\n", "    </tr>\n", "</table>"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}